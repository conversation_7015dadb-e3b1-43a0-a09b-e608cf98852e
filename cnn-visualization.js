class CNNVisualizer {
    constructor(canvasId, lossChartId) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.lossCanvas = document.getElementById(lossChartId);
        this.lossCtx = this.lossCanvas.getContext('2d');
        
        this.tooltip = document.getElementById('cnnTooltip');
        this.tooltipTitle = this.tooltip.querySelector('.tooltip-title');
        this.tooltipContent = this.tooltip.querySelector('.tooltip-content');
        
        this.network = null;
        this.isAnimating = false;
        this.animationFrame = null;
        this.animationStep = 0;
        this.maxAnimationSteps = 120;
        
        this.layers = [];
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        this.canvas.addEventListener('mousemove', (e) => {
            this.handleMouseMove(e);
        });
        
        this.canvas.addEventListener('mouseleave', () => {
            this.hideTooltip();
        });
    }
    
    setNetwork(network) {
        this.network = network;
        this.setupLayers();
    }
    
    setupLayers() {
        if (!this.network) return;
        
        this.layers = [
            { type: 'input', x: 50, y: 100, width: 120, height: 120, label: 'Input\n28×28' },
            { type: 'conv', x: 220, y: 80, width: 140, height: 160, label: 'Conv Layer\n8 Filters' },
            { type: 'pool', x: 410, y: 100, width: 100, height: 120, label: 'Max Pool\n2×2' },
            { type: 'flatten', x: 560, y: 150, width: 80, height: 20, label: 'Flatten' },
            { type: 'dense', x: 690, y: 120, width: 100, height: 80, label: 'Dense\n64 Units' },
            { type: 'output', x: 840, y: 130, width: 80, height: 60, label: 'Output\n10 Classes' }
        ];
    }
    
    draw() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        if (!this.network) {
            this.drawPlaceholder();
            return;
        }
        
        this.drawConnections();
        this.drawLayers();
        this.drawFeatureMaps();
        this.drawLabels();
    }
    
    drawPlaceholder() {
        this.ctx.fillStyle = '#666';
        this.ctx.font = '24px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('CNN Architecture will appear here', this.canvas.width / 2, this.canvas.height / 2);
    }
    
    drawConnections() {
        this.ctx.strokeStyle = '#ddd';
        this.ctx.lineWidth = 2;
        
        for (let i = 0; i < this.layers.length - 1; i++) {
            const from = this.layers[i];
            const to = this.layers[i + 1];
            
            this.ctx.beginPath();
            this.ctx.moveTo(from.x + from.width, from.y + from.height / 2);
            this.ctx.lineTo(to.x, to.y + to.height / 2);
            this.ctx.stroke();
        }
    }
    
    drawLayers() {
        for (const layer of this.layers) {
            // Layer background
            this.ctx.fillStyle = this.getLayerColor(layer.type);
            this.ctx.fillRect(layer.x, layer.y, layer.width, layer.height);
            
            // Layer border
            this.ctx.strokeStyle = '#333';
            this.ctx.lineWidth = 2;
            this.ctx.strokeRect(layer.x, layer.y, layer.width, layer.height);
            
            // Layer-specific visualizations
            this.drawLayerDetails(layer);
        }
    }
    
    getLayerColor(type) {
        const colors = {
            input: 'rgba(76, 175, 80, 0.3)',
            conv: 'rgba(33, 150, 243, 0.3)',
            pool: 'rgba(255, 152, 0, 0.3)',
            flatten: 'rgba(156, 39, 176, 0.3)',
            dense: 'rgba(244, 67, 54, 0.3)',
            output: 'rgba(96, 125, 139, 0.3)'
        };
        return colors[type] || 'rgba(200, 200, 200, 0.3)';
    }
    
    drawLayerDetails(layer) {
        const ctx = this.ctx;
        
        switch (layer.type) {
            case 'input':
                this.drawInputImage(layer);
                break;
            case 'conv':
                this.drawConvFilters(layer);
                break;
            case 'pool':
                this.drawPoolingGrid(layer);
                break;
            case 'flatten':
                this.drawFlattenArrow(layer);
                break;
            case 'dense':
                this.drawDenseNodes(layer);
                break;
            case 'output':
                this.drawOutputNodes(layer);
                break;
        }
    }
    
    drawInputImage(layer) {
        const ctx = this.ctx;
        const gridSize = 8;
        const cellSize = (layer.width - 20) / gridSize;
        
        ctx.fillStyle = '#333';
        for (let i = 0; i < gridSize; i++) {
            for (let j = 0; j < gridSize; j++) {
                const intensity = Math.random() * 0.5 + 0.2;
                ctx.fillStyle = `rgba(0, 0, 0, ${intensity})`;
                ctx.fillRect(
                    layer.x + 10 + j * cellSize,
                    layer.y + 10 + i * cellSize,
                    cellSize - 1,
                    cellSize - 1
                );
            }
        }
    }
    
    drawConvFilters(layer) {
        const ctx = this.ctx;
        const filterSize = 25;
        const spacing = 5;
        const filtersPerRow = 4;
        
        for (let f = 0; f < 8; f++) {
            const row = Math.floor(f / filtersPerRow);
            const col = f % filtersPerRow;
            const x = layer.x + 10 + col * (filterSize + spacing);
            const y = layer.y + 20 + row * (filterSize + spacing);
            
            // Feature map
            ctx.fillStyle = `hsl(${f * 45}, 70%, 60%)`;
            ctx.fillRect(x, y, filterSize, filterSize);
            
            // Filter border
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 1;
            ctx.strokeRect(x, y, filterSize, filterSize);
        }
    }
    
    drawPoolingGrid(layer) {
        const ctx = this.ctx;
        const gridSize = 6;
        const cellSize = (layer.width - 20) / gridSize;
        
        for (let i = 0; i < gridSize; i++) {
            for (let j = 0; j < gridSize; j++) {
                const intensity = Math.random() * 0.7 + 0.3;
                ctx.fillStyle = `rgba(255, 152, 0, ${intensity})`;
                ctx.fillRect(
                    layer.x + 10 + j * cellSize,
                    layer.y + 10 + i * cellSize,
                    cellSize - 1,
                    cellSize - 1
                );
            }
        }
    }
    
    drawFlattenArrow(layer) {
        const ctx = this.ctx;
        ctx.fillStyle = '#333';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('→', layer.x + layer.width / 2, layer.y + layer.height / 2 + 4);
    }
    
    drawDenseNodes(layer) {
        const ctx = this.ctx;
        const nodeCount = 8;
        const nodeSize = 8;
        const spacing = (layer.height - 20) / (nodeCount - 1);
        
        ctx.fillStyle = '#f44336';
        for (let i = 0; i < nodeCount; i++) {
            const y = layer.y + 10 + i * spacing;
            ctx.beginPath();
            ctx.arc(layer.x + layer.width / 2, y, nodeSize / 2, 0, 2 * Math.PI);
            ctx.fill();
        }
    }
    
    drawOutputNodes(layer) {
        const ctx = this.ctx;
        const nodeCount = 10;
        const nodeSize = 6;
        const spacing = (layer.height - 10) / (nodeCount - 1);
        
        for (let i = 0; i < nodeCount; i++) {
            const y = layer.y + 5 + i * spacing;
            const intensity = this.network && this.network.layerOutputs.output ? 
                            this.network.layerOutputs.output[i] : Math.random() * 0.5;
            
            ctx.fillStyle = `rgba(96, 125, 139, ${0.3 + intensity * 0.7})`;
            ctx.beginPath();
            ctx.arc(layer.x + layer.width / 2, y, nodeSize / 2, 0, 2 * Math.PI);
            ctx.fill();
            
            // Class label
            ctx.fillStyle = '#333';
            ctx.font = '10px Arial';
            ctx.textAlign = 'left';
            ctx.fillText(i.toString(), layer.x + layer.width + 5, y + 3);
        }
    }
    
    drawFeatureMaps() {
        if (!this.network || !this.network.layerOutputs.conv) return;
        
        // Draw feature map previews for conv layer
        const convLayer = this.layers.find(l => l.type === 'conv');
        if (!convLayer) return;
        
        const featureMaps = this.network.layerOutputs.conv;
        const previewSize = 15;
        const spacing = 3;
        
        for (let f = 0; f < Math.min(4, featureMaps.length); f++) {
            const x = convLayer.x + convLayer.width + 10;
            const y = convLayer.y + f * (previewSize + spacing);
            
            this.ctx.fillStyle = `hsl(${f * 90}, 60%, 50%)`;
            this.ctx.fillRect(x, y, previewSize, previewSize);
            this.ctx.strokeStyle = '#333';
            this.ctx.lineWidth = 1;
            this.ctx.strokeRect(x, y, previewSize, previewSize);
        }
    }
    
    drawLabels() {
        this.ctx.fillStyle = '#333';
        this.ctx.font = 'bold 12px Arial';
        this.ctx.textAlign = 'center';
        
        for (const layer of this.layers) {
            const lines = layer.label.split('\n');
            for (let i = 0; i < lines.length; i++) {
                this.ctx.fillText(
                    lines[i],
                    layer.x + layer.width / 2,
                    layer.y - 10 + i * 14
                );
            }
        }
    }
    
    handleMouseMove(e) {
        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        const hoveredLayer = this.getLayerAt(x, y);
        if (hoveredLayer) {
            this.showLayerTooltip(hoveredLayer, e.clientX, e.clientY);
        } else {
            this.hideTooltip();
        }
    }
    
    getLayerAt(x, y) {
        for (const layer of this.layers) {
            if (x >= layer.x && x <= layer.x + layer.width &&
                y >= layer.y && y <= layer.y + layer.height) {
                return layer;
            }
        }
        return null;
    }
    
    showLayerTooltip(layer, clientX, clientY) {
        this.tooltipTitle.textContent = this.getLayerTitle(layer.type);
        
        let content = this.getLayerDescription(layer.type);
        if (this.network) {
            const layerInfo = this.network.getLayerInfo(layer.type);
            if (layerInfo) {
                content += this.formatLayerInfo(layerInfo);
            }
        }
        
        this.tooltipContent.innerHTML = content;
        this.showTooltip(clientX, clientY);
    }
    
    getLayerTitle(type) {
        const titles = {
            input: 'Input Layer',
            conv: 'Convolutional Layer',
            pool: 'Max Pooling Layer',
            flatten: 'Flatten Layer',
            dense: 'Dense Layer',
            output: 'Output Layer'
        };
        return titles[type] || 'Unknown Layer';
    }
    
    getLayerDescription(type) {
        const descriptions = {
            input: '<div><strong>Function:</strong> Receives input image data</div><div><strong>Operation:</strong> Pixel intensity normalization</div>',
            conv: '<div><strong>Function:</strong> Feature detection using filters</div><div><strong>Operation:</strong> Convolution + ReLU activation</div>',
            pool: '<div><strong>Function:</strong> Spatial dimension reduction</div><div><strong>Operation:</strong> Max pooling operation</div>',
            flatten: '<div><strong>Function:</strong> Reshape for dense layers</div><div><strong>Operation:</strong> 2D → 1D conversion</div>',
            dense: '<div><strong>Function:</strong> High-level feature learning</div><div><strong>Operation:</strong> Fully connected + ReLU</div>',
            output: '<div><strong>Function:</strong> Classification decision</div><div><strong>Operation:</strong> Softmax probability</div>'
        };
        return descriptions[type] || '';
    }
    
    formatLayerInfo(info) {
        let content = '<div style="margin-top: 8px; border-top: 1px solid #444; padding-top: 8px;">';
        content += `<div><strong>Type:</strong> ${info.type}</div>`;
        if (info.shape) content += `<div><strong>Shape:</strong> ${info.shape}</div>`;
        if (info.filters) content += `<div><strong>Filters:</strong> ${info.filters}</div>`;
        if (info.units) content += `<div><strong>Units:</strong> ${info.units}</div>`;
        if (info.activation) content += `<div><strong>Activation:</strong> ${info.activation}</div>`;
        if (info.poolSize) content += `<div><strong>Pool Size:</strong> ${info.poolSize}</div>`;
        content += '</div>';
        return content;
    }
    
    showTooltip(clientX, clientY) {
        this.tooltip.style.left = (clientX + 10) + 'px';
        this.tooltip.style.top = (clientY - 10) + 'px';
        this.tooltip.classList.add('visible');
    }
    
    hideTooltip() {
        this.tooltip.classList.remove('visible');
    }
    
    animateForwardPass() {
        if (this.isAnimating) return;
        
        this.isAnimating = true;
        this.animationStep = 0;
        
        const animate = () => {
            // Animation logic here
            this.draw();
            
            this.animationStep++;
            if (this.animationStep < this.maxAnimationSteps) {
                this.animationFrame = requestAnimationFrame(animate);
            } else {
                this.isAnimating = false;
            }
        };
        
        animate();
    }
    
    stopAnimation() {
        this.isAnimating = false;
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
        }
    }
    
    drawLossChart(lossHistory) {
        this.lossCtx.clearRect(0, 0, this.lossCanvas.width, this.lossCanvas.height);
        
        if (lossHistory.length < 2) return;
        
        const maxLoss = Math.max(...lossHistory);
        const minLoss = Math.min(...lossHistory);
        const range = maxLoss - minLoss || 1;
        
        this.lossCtx.strokeStyle = '#667eea';
        this.lossCtx.lineWidth = 2;
        this.lossCtx.beginPath();
        
        for (let i = 0; i < lossHistory.length; i++) {
            const x = (i / (lossHistory.length - 1)) * this.lossCanvas.width;
            const y = this.lossCanvas.height - ((lossHistory[i] - minLoss) / range) * this.lossCanvas.height;
            
            if (i === 0) {
                this.lossCtx.moveTo(x, y);
            } else {
                this.lossCtx.lineTo(x, y);
            }
        }
        
        this.lossCtx.stroke();
    }
}
