class CNNVisualizer {
    constructor(canvasId, lossChartId) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.lossCanvas = document.getElementById(lossChartId);
        this.lossCtx = this.lossCanvas.getContext('2d');
        
        this.tooltip = document.getElementById('cnnTooltip');
        this.tooltipTitle = this.tooltip.querySelector('.tooltip-title');
        this.tooltipContent = this.tooltip.querySelector('.tooltip-content');
        
        this.network = null;
        this.isAnimating = false;
        this.animationFrame = null;
        this.animationStep = 0;
        this.maxAnimationSteps = 120;
        
        this.layers = [];
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        this.canvas.addEventListener('mousemove', (e) => {
            this.handleMouseMove(e);
        });
        
        this.canvas.addEventListener('mouseleave', () => {
            this.hideTooltip();
        });
    }
    
    setNetwork(network) {
        this.network = network;
        this.setupLayers();
    }

    updateNetworkState(network) {
        this.network = network;

        // Update neuron activations
        if (network.layerOutputs) {
            // Update dense layer neurons
            const denseLayer = this.layers.find(l => l.type === 'dense');
            if (denseLayer && denseLayer.neurons && network.layerOutputs.dense) {
                for (let i = 0; i < denseLayer.neurons.length; i++) {
                    if (network.layerOutputs.dense[i] !== undefined) {
                        denseLayer.neurons[i].activation = network.layerOutputs.dense[i];
                    }
                }
            }

            // Update output layer neurons
            const outputLayer = this.layers.find(l => l.type === 'output');
            if (outputLayer && outputLayer.neurons && network.layerOutputs.output) {
                for (let i = 0; i < outputLayer.neurons.length; i++) {
                    if (network.layerOutputs.output[i] !== undefined) {
                        outputLayer.neurons[i].activation = network.layerOutputs.output[i];
                    }
                }
            }

            // Update feature map activations
            const convLayer = this.layers.find(l => l.type === 'conv');
            if (convLayer && convLayer.featureMaps && network.layerOutputs.conv) {
                for (let i = 0; i < convLayer.featureMaps.length; i++) {
                    if (network.layerOutputs.conv[i]) {
                        // Calculate average activation for visualization
                        const map = network.layerOutputs.conv[i];
                        let sum = 0, count = 0;
                        for (let row = 0; row < map.length; row++) {
                            for (let col = 0; col < map[row].length; col++) {
                                sum += map[row][col];
                                count++;
                            }
                        }
                        convLayer.featureMaps[i].activation = count > 0 ? sum / count : 0;
                    }
                }
            }
        }

        // Update connection weights for dense to output
        if (network.outputWeights) {
            const denseLayer = this.layers.find(l => l.type === 'dense');
            const outputLayer = this.layers.find(l => l.type === 'output');

            if (denseLayer && outputLayer) {
                for (const connection of this.connections) {
                    if (connection.type === 'neuron-to-neuron' &&
                        connection.from && connection.to &&
                        denseLayer.neurons.includes(connection.from) &&
                        outputLayer.neurons.includes(connection.to)) {

                        const fromIndex = connection.from.index;
                        const toIndex = connection.to.index;

                        if (network.outputWeights[fromIndex] &&
                            network.outputWeights[fromIndex][toIndex] !== undefined) {
                            connection.weight = network.outputWeights[fromIndex][toIndex];
                        }
                    }
                }
            }
        }
    }
    
    setupLayers() {
        if (!this.network) return;

        this.layers = [
            {
                type: 'input',
                x: 50, y: 100,
                width: 120, height: 120,
                label: 'Input\n28×28',
                neurons: [],
                activations: []
            },
            {
                type: 'conv',
                x: 220, y: 80,
                width: 140, height: 160,
                label: `Conv Layer\n${this.network.config.convFilters} Filters`,
                neurons: [],
                activations: [],
                featureMaps: []
            },
            {
                type: 'pool',
                x: 410, y: 100,
                width: 100, height: 120,
                label: `Max Pool\n${this.network.config.poolingSize}×${this.network.config.poolingSize}`,
                neurons: [],
                activations: []
            },
            {
                type: 'flatten',
                x: 560, y: 150,
                width: 80, height: 20,
                label: 'Flatten',
                neurons: [],
                activations: []
            },
            {
                type: 'dense',
                x: 690, y: 120,
                width: 100, height: 80,
                label: `Dense\n${this.network.config.denseUnits} Units`,
                neurons: [],
                activations: []
            },
            {
                type: 'output',
                x: 840, y: 130,
                width: 80, height: 60,
                label: 'Output\n10 Classes',
                neurons: [],
                activations: []
            }
        ];

        // Initialize neuron positions for interactive layers
        this.initializeNeuronPositions();

        // Initialize connections between layers
        this.connections = [];
        this.initializeConnections();

        // Animation state
        this.activeNeurons = new Set();
        this.activeConnections = new Set();
        this.activeFeatureMaps = new Set();
    }

    initializeNeuronPositions() {
        // Dense layer neurons
        const denseLayer = this.layers.find(l => l.type === 'dense');
        if (denseLayer) {
            const neuronCount = Math.min(8, this.network.config.denseUnits); // Show max 8 for visualization
            const spacing = (denseLayer.height - 20) / (neuronCount - 1);

            for (let i = 0; i < neuronCount; i++) {
                denseLayer.neurons.push({
                    x: denseLayer.x + denseLayer.width / 2,
                    y: denseLayer.y + 10 + i * spacing,
                    radius: 8,
                    activation: 0,
                    index: i
                });
            }
        }

        // Output layer neurons
        const outputLayer = this.layers.find(l => l.type === 'output');
        if (outputLayer) {
            const spacing = (outputLayer.height - 10) / 9; // 10 classes

            for (let i = 0; i < 10; i++) {
                outputLayer.neurons.push({
                    x: outputLayer.x + outputLayer.width / 2,
                    y: outputLayer.y + 5 + i * spacing,
                    radius: 6,
                    activation: 0,
                    index: i,
                    class: i
                });
            }
        }

        // Conv layer feature maps
        const convLayer = this.layers.find(l => l.type === 'conv');
        if (convLayer) {
            const filtersPerRow = 4;
            const filterSize = 25;
            const spacing = 5;

            for (let f = 0; f < this.network.config.convFilters; f++) {
                const row = Math.floor(f / filtersPerRow);
                const col = f % filtersPerRow;

                convLayer.featureMaps.push({
                    x: convLayer.x + 10 + col * (filterSize + spacing),
                    y: convLayer.y + 20 + row * (filterSize + spacing),
                    width: filterSize,
                    height: filterSize,
                    index: f,
                    activation: 0
                });
            }
        }
    }

    initializeConnections() {
        // Create connections between layers for animation
        for (let i = 0; i < this.layers.length - 1; i++) {
            const fromLayer = this.layers[i];
            const toLayer = this.layers[i + 1];

            // Different connection types based on layer types
            if (fromLayer.type === 'dense' && toLayer.type === 'output') {
                // Dense to output connections
                for (const fromNeuron of fromLayer.neurons) {
                    for (const toNeuron of toLayer.neurons) {
                        this.connections.push({
                            from: fromNeuron,
                            to: toNeuron,
                            weight: Math.random() * 2 - 1,
                            active: false,
                            type: 'neuron-to-neuron'
                        });
                    }
                }
            } else {
                // Layer-to-layer connections for other types
                this.connections.push({
                    from: { x: fromLayer.x + fromLayer.width, y: fromLayer.y + fromLayer.height / 2 },
                    to: { x: toLayer.x, y: toLayer.y + toLayer.height / 2 },
                    active: false,
                    type: 'layer-to-layer'
                });
            }
        }
    }
    
    draw() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        if (!this.network) {
            this.drawPlaceholder();
            return;
        }
        
        this.drawConnections();
        this.drawLayers();
        this.drawFeatureMaps();
        this.drawLabels();
    }
    
    drawPlaceholder() {
        this.ctx.fillStyle = '#666';
        this.ctx.font = '24px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('CNN Architecture will appear here', this.canvas.width / 2, this.canvas.height / 2);
    }
    
    drawConnections() {
        for (const connection of this.connections) {
            if (connection.type === 'neuron-to-neuron') {
                // Draw neuron-to-neuron connections with weights
                const opacity = Math.abs(connection.weight || 0.5);
                const color = (connection.weight || 0) > 0 ? 'rgba(76, 175, 80, ' : 'rgba(244, 67, 54, ';

                this.ctx.strokeStyle = color + Math.min(opacity, 1) + ')';
                this.ctx.lineWidth = Math.abs(connection.weight || 0.5) * 3 + 1;

                if (connection.active) {
                    this.ctx.lineWidth += 2;
                    this.ctx.strokeStyle = 'rgba(255, 193, 7, 0.8)';
                }

                this.ctx.beginPath();
                this.ctx.moveTo(connection.from.x, connection.from.y);
                this.ctx.lineTo(connection.to.x, connection.to.y);
                this.ctx.stroke();
            } else {
                // Draw layer-to-layer connections
                this.ctx.strokeStyle = connection.active ? 'rgba(255, 193, 7, 0.8)' : '#ddd';
                this.ctx.lineWidth = connection.active ? 4 : 2;

                this.ctx.beginPath();
                this.ctx.moveTo(connection.from.x, connection.from.y);
                this.ctx.lineTo(connection.to.x, connection.to.y);
                this.ctx.stroke();
            }
        }
    }
    
    drawLayers() {
        for (const layer of this.layers) {
            // Layer background
            this.ctx.fillStyle = this.getLayerColor(layer.type);
            this.ctx.fillRect(layer.x, layer.y, layer.width, layer.height);
            
            // Layer border
            this.ctx.strokeStyle = '#333';
            this.ctx.lineWidth = 2;
            this.ctx.strokeRect(layer.x, layer.y, layer.width, layer.height);
            
            // Layer-specific visualizations
            this.drawLayerDetails(layer);
        }
    }
    
    getLayerColor(type) {
        const colors = {
            input: 'rgba(76, 175, 80, 0.3)',
            conv: 'rgba(33, 150, 243, 0.3)',
            pool: 'rgba(255, 152, 0, 0.3)',
            flatten: 'rgba(156, 39, 176, 0.3)',
            dense: 'rgba(244, 67, 54, 0.3)',
            output: 'rgba(96, 125, 139, 0.3)'
        };
        return colors[type] || 'rgba(200, 200, 200, 0.3)';
    }
    
    drawLayerDetails(layer) {
        const ctx = this.ctx;

        switch (layer.type) {
            case 'input':
                this.drawInputImage(layer);
                break;
            case 'conv':
                this.drawConvFilters(layer);
                break;
            case 'pool':
                this.drawPoolingGrid(layer);
                break;
            case 'flatten':
                this.drawFlattenArrow(layer);
                break;
            case 'dense':
                this.drawDenseNodes(layer);
                break;
            case 'output':
                this.drawOutputNodes(layer);
                break;
        }

        // Draw interactive neurons for dense and output layers
        this.drawInteractiveNeurons(layer);
    }

    drawInteractiveNeurons(layer) {
        if (!layer.neurons || layer.neurons.length === 0) return;

        for (const neuron of layer.neurons) {
            // Update activation from network if available
            if (this.network && this.network.layerOutputs) {
                if (layer.type === 'dense' && this.network.layerOutputs.dense) {
                    neuron.activation = this.network.layerOutputs.dense[neuron.index] || 0;
                } else if (layer.type === 'output' && this.network.layerOutputs.output) {
                    neuron.activation = this.network.layerOutputs.output[neuron.index] || 0;
                }
            }

            // Neuron circle with activation-based color
            const intensity = Math.abs(neuron.activation);
            const color = neuron.activation > 0 ?
                `rgba(76, 175, 80, ${0.3 + intensity * 0.7})` :
                `rgba(244, 67, 54, ${0.3 + intensity * 0.7})`;

            this.ctx.fillStyle = color;
            this.ctx.beginPath();
            this.ctx.arc(neuron.x, neuron.y, neuron.radius, 0, 2 * Math.PI);
            this.ctx.fill();

            // Neuron border with active state
            this.ctx.strokeStyle = this.activeNeurons.has(neuron) ?
                'rgba(255, 193, 7, 1)' : 'rgba(0, 0, 0, 0.3)';
            this.ctx.lineWidth = this.activeNeurons.has(neuron) ? 3 : 1;
            this.ctx.stroke();

            // Activation value
            this.ctx.fillStyle = 'black';
            this.ctx.font = '10px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText(
                neuron.activation.toFixed(2),
                neuron.x,
                neuron.y + 3
            );

            // Class label for output neurons
            if (layer.type === 'output') {
                this.ctx.fillStyle = '#333';
                this.ctx.font = '10px Arial';
                this.ctx.textAlign = 'left';
                this.ctx.fillText(neuron.class.toString(), neuron.x + neuron.radius + 5, neuron.y + 3);
            }
        }
    }
    
    drawInputImage(layer) {
        const ctx = this.ctx;
        const gridSize = 8;
        const cellSize = (layer.width - 20) / gridSize;
        
        ctx.fillStyle = '#333';
        for (let i = 0; i < gridSize; i++) {
            for (let j = 0; j < gridSize; j++) {
                const intensity = Math.random() * 0.5 + 0.2;
                ctx.fillStyle = `rgba(0, 0, 0, ${intensity})`;
                ctx.fillRect(
                    layer.x + 10 + j * cellSize,
                    layer.y + 10 + i * cellSize,
                    cellSize - 1,
                    cellSize - 1
                );
            }
        }
    }
    
    drawConvFilters(layer) {
        const ctx = this.ctx;

        if (layer.featureMaps && layer.featureMaps.length > 0) {
            for (const featureMap of layer.featureMaps) {
                // Update activation from network if available
                if (this.network && this.network.layerOutputs && this.network.layerOutputs.conv) {
                    const convOutputs = this.network.layerOutputs.conv;
                    if (convOutputs[featureMap.index]) {
                        // Calculate average activation for this feature map
                        const map = convOutputs[featureMap.index];
                        let sum = 0, count = 0;
                        for (let i = 0; i < map.length; i++) {
                            for (let j = 0; j < map[i].length; j++) {
                                sum += map[i][j];
                                count++;
                            }
                        }
                        featureMap.activation = count > 0 ? sum / count : 0;
                    }
                }

                // Feature map with activation-based intensity
                const baseHue = featureMap.index * 45;
                const intensity = 0.4 + (featureMap.activation * 0.6);
                const saturation = this.activeFeatureMaps.has(featureMap) ? 90 : 70;
                const lightness = this.activeFeatureMaps.has(featureMap) ? 70 : 60;

                ctx.fillStyle = `hsl(${baseHue}, ${saturation}%, ${lightness}%)`;
                ctx.fillRect(featureMap.x, featureMap.y, featureMap.width, featureMap.height);

                // Feature map border with active state
                ctx.strokeStyle = this.activeFeatureMaps.has(featureMap) ?
                    'rgba(255, 193, 7, 1)' : '#333';
                ctx.lineWidth = this.activeFeatureMaps.has(featureMap) ? 3 : 1;
                ctx.strokeRect(featureMap.x, featureMap.y, featureMap.width, featureMap.height);

                // Show activation value
                if (featureMap.activation > 0.01) {
                    ctx.fillStyle = 'white';
                    ctx.font = '8px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(
                        featureMap.activation.toFixed(2),
                        featureMap.x + featureMap.width / 2,
                        featureMap.y + featureMap.height / 2 + 2
                    );
                }

                // Filter index
                ctx.fillStyle = 'black';
                ctx.font = '8px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(
                    `F${featureMap.index}`,
                    featureMap.x + 2,
                    featureMap.y + 10
                );
            }
        }
    }
    
    drawPoolingGrid(layer) {
        const ctx = this.ctx;
        const gridSize = 6;
        const cellSize = (layer.width - 20) / gridSize;
        
        for (let i = 0; i < gridSize; i++) {
            for (let j = 0; j < gridSize; j++) {
                const intensity = Math.random() * 0.7 + 0.3;
                ctx.fillStyle = `rgba(255, 152, 0, ${intensity})`;
                ctx.fillRect(
                    layer.x + 10 + j * cellSize,
                    layer.y + 10 + i * cellSize,
                    cellSize - 1,
                    cellSize - 1
                );
            }
        }
    }
    
    drawFlattenArrow(layer) {
        const ctx = this.ctx;
        ctx.fillStyle = '#333';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('→', layer.x + layer.width / 2, layer.y + layer.height / 2 + 4);
    }
    
    drawDenseNodes(layer) {
        const ctx = this.ctx;
        const nodeCount = 8;
        const nodeSize = 8;
        const spacing = (layer.height - 20) / (nodeCount - 1);
        
        ctx.fillStyle = '#f44336';
        for (let i = 0; i < nodeCount; i++) {
            const y = layer.y + 10 + i * spacing;
            ctx.beginPath();
            ctx.arc(layer.x + layer.width / 2, y, nodeSize / 2, 0, 2 * Math.PI);
            ctx.fill();
        }
    }
    
    drawOutputNodes(layer) {
        const ctx = this.ctx;
        const nodeCount = 10;
        const nodeSize = 6;
        const spacing = (layer.height - 10) / (nodeCount - 1);
        
        for (let i = 0; i < nodeCount; i++) {
            const y = layer.y + 5 + i * spacing;
            const intensity = this.network && this.network.layerOutputs.output ? 
                            this.network.layerOutputs.output[i] : Math.random() * 0.5;
            
            ctx.fillStyle = `rgba(96, 125, 139, ${0.3 + intensity * 0.7})`;
            ctx.beginPath();
            ctx.arc(layer.x + layer.width / 2, y, nodeSize / 2, 0, 2 * Math.PI);
            ctx.fill();
            
            // Class label
            ctx.fillStyle = '#333';
            ctx.font = '10px Arial';
            ctx.textAlign = 'left';
            ctx.fillText(i.toString(), layer.x + layer.width + 5, y + 3);
        }
    }
    
    drawFeatureMaps() {
        if (!this.network || !this.network.layerOutputs.conv) return;
        
        // Draw feature map previews for conv layer
        const convLayer = this.layers.find(l => l.type === 'conv');
        if (!convLayer) return;
        
        const featureMaps = this.network.layerOutputs.conv;
        const previewSize = 15;
        const spacing = 3;
        
        for (let f = 0; f < Math.min(4, featureMaps.length); f++) {
            const x = convLayer.x + convLayer.width + 10;
            const y = convLayer.y + f * (previewSize + spacing);
            
            this.ctx.fillStyle = `hsl(${f * 90}, 60%, 50%)`;
            this.ctx.fillRect(x, y, previewSize, previewSize);
            this.ctx.strokeStyle = '#333';
            this.ctx.lineWidth = 1;
            this.ctx.strokeRect(x, y, previewSize, previewSize);
        }
    }
    
    drawLabels() {
        this.ctx.fillStyle = '#333';
        this.ctx.font = 'bold 12px Arial';
        this.ctx.textAlign = 'center';
        
        for (const layer of this.layers) {
            const lines = layer.label.split('\n');
            for (let i = 0; i < lines.length; i++) {
                this.ctx.fillText(
                    lines[i],
                    layer.x + layer.width / 2,
                    layer.y - 10 + i * 14
                );
            }
        }
    }
    
    handleMouseMove(e) {
        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        // Check for neuron hover first
        const hoveredNeuron = this.getNeuronAt(x, y);
        if (hoveredNeuron) {
            this.showNeuronTooltip(hoveredNeuron, e.clientX, e.clientY);
            return;
        }

        // Check for feature map hover
        const hoveredFeatureMap = this.getFeatureMapAt(x, y);
        if (hoveredFeatureMap) {
            this.showFeatureMapTooltip(hoveredFeatureMap, e.clientX, e.clientY);
            return;
        }

        // Check for connection hover
        const hoveredConnection = this.getConnectionAt(x, y);
        if (hoveredConnection) {
            this.showConnectionTooltip(hoveredConnection, e.clientX, e.clientY);
            return;
        }

        // Check for layer hover
        const hoveredLayer = this.getLayerAt(x, y);
        if (hoveredLayer) {
            this.showLayerTooltip(hoveredLayer, e.clientX, e.clientY);
        } else {
            this.hideTooltip();
        }
    }

    getNeuronAt(x, y) {
        for (const layer of this.layers) {
            if (layer.neurons) {
                for (const neuron of layer.neurons) {
                    const distance = Math.sqrt((x - neuron.x) ** 2 + (y - neuron.y) ** 2);
                    if (distance <= neuron.radius) {
                        return { ...neuron, layerType: layer.type, layerIndex: this.layers.indexOf(layer) };
                    }
                }
            }
        }
        return null;
    }

    getFeatureMapAt(x, y) {
        for (const layer of this.layers) {
            if (layer.featureMaps) {
                for (const featureMap of layer.featureMaps) {
                    if (x >= featureMap.x && x <= featureMap.x + featureMap.width &&
                        y >= featureMap.y && y <= featureMap.y + featureMap.height) {
                        return { ...featureMap, layerType: layer.type };
                    }
                }
            }
        }
        return null;
    }

    getConnectionAt(x, y) {
        const tolerance = 5;

        for (let i = 0; i < this.connections.length; i++) {
            const connection = this.connections[i];
            if (connection.type === 'neuron-to-neuron') {
                const distance = this.distanceToLine(x, y, connection.from.x, connection.from.y, connection.to.x, connection.to.y);

                if (distance <= tolerance) {
                    return { ...connection, index: i };
                }
            }
        }
        return null;
    }

    distanceToLine(px, py, x1, y1, x2, y2) {
        const A = px - x1;
        const B = py - y1;
        const C = x2 - x1;
        const D = y2 - y1;

        const dot = A * C + B * D;
        const lenSq = C * C + D * D;

        if (lenSq === 0) return Math.sqrt(A * A + B * B);

        let param = dot / lenSq;

        if (param < 0) {
            return Math.sqrt(A * A + B * B);
        } else if (param > 1) {
            const E = px - x2;
            const F = py - y2;
            return Math.sqrt(E * E + F * F);
        } else {
            const projX = x1 + param * C;
            const projY = y1 + param * D;
            const dx = px - projX;
            const dy = py - projY;
            return Math.sqrt(dx * dx + dy * dy);
        }
    }

    getLayerAt(x, y) {
        for (const layer of this.layers) {
            if (x >= layer.x && x <= layer.x + layer.width &&
                y >= layer.y && y <= layer.y + layer.height) {
                return layer;
            }
        }
        return null;
    }
    
    showNeuronTooltip(neuron, clientX, clientY) {
        const layerNames = { 'dense': 'Dense', 'output': 'Output' };
        const layerName = layerNames[neuron.layerType] || 'Unknown';

        this.tooltipTitle.textContent = `${layerName} Neuron ${neuron.index + 1}`;

        let content = `<div>Activation: <span style="color: ${neuron.activation < 0 ? '#f44336' : '#4CAF50'}; font-weight: bold;">${neuron.activation.toFixed(4)}</span></div>`;

        if (neuron.layerType === 'output') {
            content += `<div>Class: <strong>${neuron.class}</strong></div>`;
            const percentage = (neuron.activation * 100).toFixed(1);
            content += `<div>Probability: <strong>${percentage}%</strong></div>`;

            content += `<div style="margin-top: 8px; border-top: 1px solid #444; padding-top: 8px;"><strong>Classification Logic:</strong></div>`;
            content += `<div style="font-family: monospace; background: rgba(255,255,255,0.1); padding: 4px; margin: 4px 0;">`;
            content += `softmax(logits[${neuron.class}]) = ${neuron.activation.toFixed(4)}`;
            content += `</div>`;
        } else if (neuron.layerType === 'dense') {
            content += `<div>Type: Dense (Fully Connected)</div>`;
            content += `<div>Function: ReLU(Σ(w×input) + bias)</div>`;

            content += `<div style="margin-top: 8px; border-top: 1px solid #444; padding-top: 8px;"><strong>Mathematical Process:</strong></div>`;
            content += `<div style="font-family: monospace; background: rgba(255,255,255,0.1); padding: 4px; margin: 4px 0;">`;
            content += `1. Linear: z = W×flattened_features + b<br>`;
            content += `2. ReLU: activation = max(0, z)<br>`;
            content += `3. Result: ${neuron.activation.toFixed(4)}`;
            content += `</div>`;
        }

        this.tooltipContent.innerHTML = content;
        this.showTooltip(clientX, clientY);
    }

    showFeatureMapTooltip(featureMap, clientX, clientY) {
        this.tooltipTitle.textContent = `Feature Map ${featureMap.index + 1}`;

        let content = `<div>Filter Index: <strong>${featureMap.index}</strong></div>`;
        content += `<div>Activation: <span style="color: #4CAF50; font-weight: bold;">${featureMap.activation.toFixed(4)}</span></div>`;

        content += `<div style="margin-top: 8px; border-top: 1px solid #444; padding-top: 8px;"><strong>Convolution Process:</strong></div>`;
        content += `<div style="font-family: monospace; background: rgba(255,255,255,0.1); padding: 6px; margin: 4px 0; font-size: 11px;">`;

        const filterSize = this.network.config.filterSize;
        content += `<div><strong>Step 1: Filter Application</strong></div>`;
        content += `<div>Filter Size: ${filterSize}×${filterSize}</div>`;
        content += `<div>Stride: 1 (moves 1 pixel at a time)</div>`;

        content += `<div style="margin-top: 4px;"><strong>Step 2: Convolution Math</strong></div>`;
        content += `<div>For each position (i,j):</div>`;
        content += `<div>output[i,j] = Σ(filter[m,n] × input[i+m,j+n])</div>`;

        content += `<div style="margin-top: 4px;"><strong>Step 3: ReLU Activation</strong></div>`;
        content += `<div>final = max(0, convolution_result)</div>`;
        content += `<div>= ${featureMap.activation.toFixed(4)}</div>`;
        content += `</div>`;

        content += `<div><strong>Feature Detected:</strong> ${this.getFeatureDescription(featureMap.index)}</div>`;

        this.tooltipContent.innerHTML = content;
        this.showTooltip(clientX, clientY);
    }

    showConnectionTooltip(connection, clientX, clientY) {
        this.tooltipTitle.textContent = `Dense → Output Connection`;

        let content = `<div>From: Dense Neuron ${connection.from.index + 1}</div>`;
        content += `<div>To: Output Class ${connection.to.class}</div>`;
        content += `<div>Weight: <span style="color: ${connection.weight < 0 ? '#f44336' : '#4CAF50'}; font-weight: bold;">${connection.weight.toFixed(6)}</span></div>`;
        content += `<div>Magnitude: <strong>${Math.abs(connection.weight).toFixed(6)}</strong></div>`;
        content += `<div>Effect: <strong>${connection.weight > 0 ? 'Excitatory (+)' : 'Inhibitory (-)'}</strong></div>`;

        if (connection.from.activation !== undefined && connection.to.activation !== undefined) {
            const contribution = connection.weight * connection.from.activation;
            content += `<div style="margin-top: 8px; border-top: 1px solid #444; padding-top: 8px;"><strong>Current Contribution:</strong></div>`;
            content += `<div style="font-family: monospace; background: rgba(255,255,255,0.1); padding: 4px; margin: 4px 0;">`;
            content += `weight × activation = contribution<br>`;
            content += `${connection.weight.toFixed(4)} × ${connection.from.activation.toFixed(4)} = <span style="color: ${contribution < 0 ? '#f44336' : '#4CAF50'}; font-weight: bold;">${contribution.toFixed(4)}</span>`;
            content += `</div>`;
        }

        this.tooltipContent.innerHTML = content;
        this.showTooltip(clientX, clientY);
    }

    getFeatureDescription(index) {
        const features = [
            'Horizontal edges', 'Vertical edges', 'Diagonal lines', 'Curves',
            'Corners', 'Textures', 'Patterns', 'Shapes'
        ];
        return features[index % features.length];
    }

    showLayerTooltip(layer, clientX, clientY) {
        this.tooltipTitle.textContent = this.getLayerTitle(layer.type);

        let content = this.getLayerDescription(layer.type);
        if (this.network) {
            const layerInfo = this.network.getLayerInfo(layer.type);
            if (layerInfo) {
                content += this.formatLayerInfo(layerInfo);
            }
        }

        this.tooltipContent.innerHTML = content;
        this.showTooltip(clientX, clientY);
    }
    
    getLayerTitle(type) {
        const titles = {
            input: 'Input Layer',
            conv: 'Convolutional Layer',
            pool: 'Max Pooling Layer',
            flatten: 'Flatten Layer',
            dense: 'Dense Layer',
            output: 'Output Layer'
        };
        return titles[type] || 'Unknown Layer';
    }
    
    getLayerDescription(type) {
        const descriptions = {
            input: '<div><strong>Function:</strong> Receives input image data</div><div><strong>Operation:</strong> Pixel intensity normalization</div>',
            conv: '<div><strong>Function:</strong> Feature detection using filters</div><div><strong>Operation:</strong> Convolution + ReLU activation</div>',
            pool: '<div><strong>Function:</strong> Spatial dimension reduction</div><div><strong>Operation:</strong> Max pooling operation</div>',
            flatten: '<div><strong>Function:</strong> Reshape for dense layers</div><div><strong>Operation:</strong> 2D → 1D conversion</div>',
            dense: '<div><strong>Function:</strong> High-level feature learning</div><div><strong>Operation:</strong> Fully connected + ReLU</div>',
            output: '<div><strong>Function:</strong> Classification decision</div><div><strong>Operation:</strong> Softmax probability</div>'
        };
        return descriptions[type] || '';
    }
    
    formatLayerInfo(info) {
        let content = '<div style="margin-top: 8px; border-top: 1px solid #444; padding-top: 8px;">';
        content += `<div><strong>Type:</strong> ${info.type}</div>`;
        if (info.shape) content += `<div><strong>Shape:</strong> ${info.shape}</div>`;
        if (info.filters) content += `<div><strong>Filters:</strong> ${info.filters}</div>`;
        if (info.units) content += `<div><strong>Units:</strong> ${info.units}</div>`;
        if (info.activation) content += `<div><strong>Activation:</strong> ${info.activation}</div>`;
        if (info.poolSize) content += `<div><strong>Pool Size:</strong> ${info.poolSize}</div>`;
        content += '</div>';
        return content;
    }
    
    showTooltip(clientX, clientY) {
        this.tooltip.style.left = (clientX + 10) + 'px';
        this.tooltip.style.top = (clientY - 10) + 'px';
        this.tooltip.classList.add('visible');
    }
    
    hideTooltip() {
        this.tooltip.classList.remove('visible');
    }
    
    animateForwardPass() {
        if (this.isAnimating) return;

        this.isAnimating = true;
        this.animationStep = 0;

        const animate = () => {
            this.activeNeurons.clear();
            this.activeConnections.clear();
            this.activeFeatureMaps.clear();

            const progress = this.animationStep / this.maxAnimationSteps;
            const layerCount = this.layers.length;
            const layerProgress = progress * layerCount;

            // Animate through each layer
            for (let i = 0; i < layerCount; i++) {
                const layer = this.layers[i];

                if (layerProgress >= i && layerProgress < i + 1) {
                    // Current layer is active
                    if (layer.neurons) {
                        for (const neuron of layer.neurons) {
                            this.activeNeurons.add(neuron);
                        }
                    }
                    if (layer.featureMaps) {
                        for (const featureMap of layer.featureMaps) {
                            this.activeFeatureMaps.add(featureMap);
                        }
                    }

                    // Activate connections to next layer
                    if (i < layerCount - 1) {
                        const nextLayer = this.layers[i + 1];
                        for (const connection of this.connections) {
                            if (connection.type === 'neuron-to-neuron') {
                                // Check if connection is from current layer to next
                                const fromLayerIndex = this.layers.findIndex(l => l.neurons && l.neurons.includes(connection.from));
                                const toLayerIndex = this.layers.findIndex(l => l.neurons && l.neurons.includes(connection.to));

                                if (fromLayerIndex === i && toLayerIndex === i + 1) {
                                    connection.active = true;
                                    this.activeConnections.add(connection);
                                }
                            } else if (connection.type === 'layer-to-layer') {
                                // Simple layer-to-layer connection
                                const connectionIndex = this.connections.indexOf(connection);
                                if (connectionIndex === i) {
                                    connection.active = true;
                                    this.activeConnections.add(connection);
                                }
                            }
                        }
                    }
                }
            }

            this.draw();

            this.animationStep++;
            if (this.animationStep < this.maxAnimationSteps) {
                this.animationFrame = requestAnimationFrame(animate);
            } else {
                this.isAnimating = false;
                // Reset active states
                for (const connection of this.connections) {
                    connection.active = false;
                }
                this.activeNeurons.clear();
                this.activeConnections.clear();
                this.activeFeatureMaps.clear();
            }
        };

        animate();
    }
    
    stopAnimation() {
        this.isAnimating = false;
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
        }
    }
    
    drawLossChart(lossHistory) {
        this.lossCtx.clearRect(0, 0, this.lossCanvas.width, this.lossCanvas.height);
        
        if (lossHistory.length < 2) return;
        
        const maxLoss = Math.max(...lossHistory);
        const minLoss = Math.min(...lossHistory);
        const range = maxLoss - minLoss || 1;
        
        this.lossCtx.strokeStyle = '#667eea';
        this.lossCtx.lineWidth = 2;
        this.lossCtx.beginPath();
        
        for (let i = 0; i < lossHistory.length; i++) {
            const x = (i / (lossHistory.length - 1)) * this.lossCanvas.width;
            const y = this.lossCanvas.height - ((lossHistory[i] - minLoss) / range) * this.lossCanvas.height;
            
            if (i === 0) {
                this.lossCtx.moveTo(x, y);
            } else {
                this.lossCtx.lineTo(x, y);
            }
        }
        
        this.lossCtx.stroke();
    }
}
