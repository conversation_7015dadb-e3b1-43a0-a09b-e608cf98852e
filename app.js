class DeepLearningApp {
    constructor() {
        this.network = null;
        this.visualizer = null;
        this.trainingData = [
            { input: [0, 0], output: [0] },
            { input: [0, 1], output: [1] },
            { input: [1, 0], output: [1] },
            { input: [1, 1], output: [0] }
        ];
        
        this.isTraining = false;
        this.isPaused = false;
        this.trainingInterval = null;
        this.currentEpoch = 0;
        this.maxEpochs = 1000;
        
        this.initializeApp();
        this.setupEventListeners();
    }
    
    initializeApp() {
        // Initialize network with default parameters
        this.createNetwork();
        
        // Initialize visualizer
        this.visualizer = new NetworkVisualizer('networkCanvas', 'lossChart');
        this.visualizer.setupNetwork(
            this.network.inputSize,
            this.network.hiddenSize,
            this.network.outputSize
        );
        
        // Initial draw
        this.updateVisualization();
        this.updateUI();
    }
    
    createNetwork() {
        const inputSize = parseInt(document.getElementById('inputNeurons').value);
        const hiddenSize = parseInt(document.getElementById('hiddenNeurons').value);
        const outputSize = parseInt(document.getElementById('outputNeurons').value);
        const learningRate = parseFloat(document.getElementById('learningRate').value);
        
        this.network = new NeuralNetwork(inputSize, hiddenSize, outputSize, learningRate);
        this.currentEpoch = 0;
    }
    
    setupEventListeners() {
        // Control buttons
        document.getElementById('startTraining').addEventListener('click', () => {
            this.startTraining();
        });
        
        document.getElementById('forwardPass').addEventListener('click', () => {
            this.demonstrateForwardPass();
        });
        
        document.getElementById('reset').addEventListener('click', () => {
            this.resetNetwork();
        });
        
        document.getElementById('pauseResume').addEventListener('click', () => {
            this.togglePause();
        });
        
        // Parameter controls
        const controls = ['inputNeurons', 'hiddenNeurons', 'outputNeurons', 'learningRate', 'animationSpeed'];
        controls.forEach(id => {
            const element = document.getElementById(id);
            element.addEventListener('input', () => {
                this.updateParameterDisplay(id);
                if (['inputNeurons', 'hiddenNeurons', 'outputNeurons'].includes(id)) {
                    this.resetNetwork();
                } else if (id === 'learningRate') {
                    this.network.learningRate = parseFloat(element.value);
                } else if (id === 'animationSpeed') {
                    this.visualizer.animationSpeed = parseInt(element.value);
                }
            });
        });
    }
    
    updateParameterDisplay(id) {
        const element = document.getElementById(id);
        const displayElement = document.getElementById(id.replace(/([A-Z])/g, '').toLowerCase() === 'inputneurons' ? 'inputValue' :
                                                      id.replace(/([A-Z])/g, '').toLowerCase() === 'hiddenneurons' ? 'hiddenValue' :
                                                      id.replace(/([A-Z])/g, '').toLowerCase() === 'outputneurons' ? 'outputValue' :
                                                      id.replace(/([A-Z])/g, '').toLowerCase() === 'learningrate' ? 'lrValue' : 'speedValue');
        displayElement.textContent = element.value;
    }
    
    startTraining() {
        if (this.isTraining) {
            this.stopTraining();
            return;
        }
        
        this.isTraining = true;
        this.isPaused = false;
        document.getElementById('startTraining').textContent = 'Stop Training';
        document.getElementById('pauseResume').textContent = 'Pause';
        
        this.trainStep();
    }
    
    trainStep() {
        if (!this.isTraining || this.isPaused) return;
        
        // Train for one epoch
        let totalLoss = 0;
        for (const data of this.trainingData) {
            const output = this.network.backward(data.input, data.output);
            totalLoss += this.network.calculateLoss(output, data.output);
        }
        
        const avgLoss = totalLoss / this.trainingData.length;
        this.network.lossHistory.push(avgLoss);
        this.currentEpoch++;
        
        // Update visualization
        this.updateVisualization();
        this.updateUI();
        
        // Check stopping conditions
        if (this.currentEpoch >= this.maxEpochs || avgLoss < 0.001) {
            this.stopTraining();
            return;
        }
        
        // Schedule next training step
        const speed = parseInt(document.getElementById('animationSpeed').value);
        const delay = Math.max(50, 500 - speed * 50);
        
        this.trainingInterval = setTimeout(() => {
            this.trainStep();
        }, delay);
    }
    
    stopTraining() {
        this.isTraining = false;
        this.isPaused = false;
        document.getElementById('startTraining').textContent = 'Start Training';
        document.getElementById('pauseResume').textContent = 'Pause';
        
        if (this.trainingInterval) {
            clearTimeout(this.trainingInterval);
            this.trainingInterval = null;
        }
    }
    
    togglePause() {
        if (!this.isTraining) return;
        
        this.isPaused = !this.isPaused;
        document.getElementById('pauseResume').textContent = this.isPaused ? 'Resume' : 'Pause';
        
        if (!this.isPaused) {
            this.trainStep();
        }
    }
    
    demonstrateForwardPass() {
        if (this.visualizer.isAnimating) return;
        
        // Use a random training example
        const randomIndex = Math.floor(Math.random() * this.trainingData.length);
        const data = this.trainingData[randomIndex];
        
        // Show which data point we're using
        console.log(`Forward pass with input: [${data.input.join(', ')}] → Expected output: ${data.output[0]}`);
        
        this.visualizer.animateForwardPass(this.network, data.input);
    }
    
    resetNetwork() {
        this.stopTraining();
        this.visualizer.stopAnimation();
        
        this.createNetwork();
        this.visualizer.setupNetwork(
            this.network.inputSize,
            this.network.hiddenSize,
            this.network.outputSize
        );
        
        this.updateVisualization();
        this.updateUI();
    }
    
    updateVisualization() {
        // Update network state in visualizer
        this.visualizer.updateNetworkState(this.network);
        this.visualizer.draw();
        
        // Update loss chart
        this.visualizer.drawLossChart(this.network.lossHistory);
    }
    
    updateUI() {
        // Update training progress
        document.getElementById('epochCount').textContent = this.currentEpoch;
        
        const latestLoss = this.network.lossHistory.length > 0 ? 
            this.network.lossHistory[this.network.lossHistory.length - 1] : 1.0;
        document.getElementById('lossValue').textContent = latestLoss.toFixed(3);
        
        const accuracy = this.network.getAccuracy(this.trainingData);
        document.getElementById('accuracyValue').textContent = accuracy.toFixed(0) + '%';
        
        // Update parameter displays
        this.updateParameterDisplay('inputNeurons');
        this.updateParameterDisplay('hiddenNeurons');
        this.updateParameterDisplay('outputNeurons');
        this.updateParameterDisplay('learningRate');
        this.updateParameterDisplay('animationSpeed');
    }
}

// Initialize the app when the page loads
document.addEventListener('DOMContentLoaded', () => {
    const app = new DeepLearningApp();
    
    // Make it globally accessible for debugging
    window.deepLearningApp = app;
    
    console.log('🧠 Neural Network Visualizer loaded!');
    console.log('Try clicking "Forward Pass" to see data flow through the network');
    console.log('Click "Start Training" to watch the network learn the XOR problem');
});
