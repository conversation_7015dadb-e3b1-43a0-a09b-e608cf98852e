class CNNApp {
    constructor() {
        this.network = null;
        this.visualizer = null;
        this.isTraining = false;
        this.isPaused = false;
        this.trainingInterval = null;
        this.currentEpoch = 0;
        this.maxEpochs = 100;
        
        // Sample digit patterns (simplified 8x8 representations)
        this.sampleDigits = this.generateSampleDigits();
        this.currentTestImage = null;
        
        this.initializeApp();
        this.setupEventListeners();
        this.setupDrawingCanvas();
    }
    
    initializeApp() {
        this.createNetwork();
        
        this.visualizer = new CNNVisualizer('cnnCanvas', 'cnnLossChart');
        this.visualizer.setNetwork(this.network);
        
        this.updateVisualization();
        this.updateUI();
        this.generateTestImage();
    }
    
    createNetwork() {
        const config = {
            convFilters: parseInt(document.getElementById('convFilters').value),
            filterSize: parseInt(document.getElementById('filterSize').value),
            poolingSize: parseInt(document.getElementById('poolingSize').value),
            denseUnits: parseInt(document.getElementById('denseUnits').value),
            learningRate: parseFloat(document.getElementById('cnnLearningRate').value)
        };
        
        this.network = new ConvolutionalNeuralNetwork(config);
        this.currentEpoch = 0;
    }
    
    setupEventListeners() {
        // Training controls
        document.getElementById('startCNNTraining').addEventListener('click', () => {
            this.startTraining();
        });
        
        document.getElementById('forwardPassCNN').addEventListener('click', () => {
            this.demonstrateForwardPass();
        });
        
        document.getElementById('resetCNN').addEventListener('click', () => {
            this.resetNetwork();
        });
        
        document.getElementById('pauseResumeCNN').addEventListener('click', () => {
            this.togglePause();
        });
        
        // Architecture controls
        const controls = ['convFilters', 'filterSize', 'poolingSize', 'denseUnits', 'cnnLearningRate', 'batchSize', 'cnnAnimationSpeed'];
        controls.forEach(id => {
            const element = document.getElementById(id);
            element.addEventListener('input', () => {
                this.updateParameterDisplay(id);
                if (['convFilters', 'filterSize', 'poolingSize', 'denseUnits'].includes(id)) {
                    this.resetNetwork();
                } else if (id === 'cnnLearningRate') {
                    this.network.config.learningRate = parseFloat(element.value);
                }
            });
        });
        
        // Image classification controls
        document.getElementById('imageSelect').addEventListener('change', (e) => {
            this.loadSampleImage(e.target.value);
        });
        
        document.getElementById('classifyImage').addEventListener('click', () => {
            this.classifyCurrentImage();
        });
        
        document.getElementById('clearDrawing').addEventListener('click', () => {
            this.clearDrawingCanvas();
        });
    }
    
    setupDrawingCanvas() {
        this.drawingCanvas = document.getElementById('drawingCanvas');
        this.drawingCtx = this.drawingCanvas.getContext('2d');
        this.isDrawing = false;
        
        // Set up drawing events
        this.drawingCanvas.addEventListener('mousedown', (e) => {
            this.isDrawing = true;
            this.draw(e);
        });
        
        this.drawingCanvas.addEventListener('mousemove', (e) => {
            if (this.isDrawing) this.draw(e);
        });
        
        this.drawingCanvas.addEventListener('mouseup', () => {
            this.isDrawing = false;
        });
        
        this.drawingCanvas.addEventListener('mouseout', () => {
            this.isDrawing = false;
        });
        
        // Touch events for mobile
        this.drawingCanvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            this.isDrawing = true;
            const touch = e.touches[0];
            const mouseEvent = new MouseEvent('mousedown', {
                clientX: touch.clientX,
                clientY: touch.clientY
            });
            this.drawingCanvas.dispatchEvent(mouseEvent);
        });
        
        this.drawingCanvas.addEventListener('touchmove', (e) => {
            e.preventDefault();
            if (this.isDrawing) {
                const touch = e.touches[0];
                const mouseEvent = new MouseEvent('mousemove', {
                    clientX: touch.clientX,
                    clientY: touch.clientY
                });
                this.drawingCanvas.dispatchEvent(mouseEvent);
            }
        });
        
        this.drawingCanvas.addEventListener('touchend', (e) => {
            e.preventDefault();
            this.isDrawing = false;
        });
        
        this.clearDrawingCanvas();
    }
    
    draw(e) {
        const rect = this.drawingCanvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        this.drawingCtx.fillStyle = '#000';
        this.drawingCtx.beginPath();
        this.drawingCtx.arc(x, y, 8, 0, 2 * Math.PI);
        this.drawingCtx.fill();
    }
    
    clearDrawingCanvas() {
        this.drawingCtx.fillStyle = '#fff';
        this.drawingCtx.fillRect(0, 0, this.drawingCanvas.width, this.drawingCanvas.height);
        
        // Add border
        this.drawingCtx.strokeStyle = '#ddd';
        this.drawingCtx.lineWidth = 1;
        this.drawingCtx.strokeRect(0, 0, this.drawingCanvas.width, this.drawingCanvas.height);
    }
    
    generateSampleDigits() {
        // Generate simple 28x28 digit patterns
        const digits = {};
        
        // Simplified digit patterns (you would normally load real MNIST data)
        for (let digit = 0; digit < 10; digit++) {
            const pattern = [];
            for (let i = 0; i < 28; i++) {
                pattern[i] = [];
                for (let j = 0; j < 28; j++) {
                    // Create simple patterns for each digit
                    pattern[i][j] = this.generateDigitPixel(digit, i, j);
                }
            }
            digits[`digit${digit}`] = pattern;
        }
        
        return digits;
    }
    
    generateDigitPixel(digit, row, col) {
        // Simple pattern generation for demonstration
        const centerX = 14, centerY = 14;
        const distFromCenter = Math.sqrt((row - centerY) ** 2 + (col - centerX) ** 2);
        
        switch (digit) {
            case 0:
                return (distFromCenter > 6 && distFromCenter < 10) ? 0.8 + Math.random() * 0.2 : Math.random() * 0.1;
            case 1:
                return (col > 12 && col < 16) ? 0.8 + Math.random() * 0.2 : Math.random() * 0.1;
            case 2:
                return (row < 8 || row > 20 || (row > 12 && row < 16)) ? 0.8 + Math.random() * 0.2 : Math.random() * 0.1;
            default:
                return Math.random() * 0.3 + (Math.sin(row * 0.3 + digit) + Math.cos(col * 0.3 + digit)) * 0.2;
        }
    }
    
    loadSampleImage(imageKey) {
        if (this.sampleDigits[imageKey]) {
            this.currentTestImage = this.sampleDigits[imageKey];
            this.drawImageOnCanvas(this.currentTestImage);
        }
    }
    
    drawImageOnCanvas(imageData) {
        const canvas = this.drawingCanvas;
        const ctx = this.drawingCtx;
        const cellWidth = canvas.width / 28;
        const cellHeight = canvas.height / 28;
        
        for (let i = 0; i < 28; i++) {
            for (let j = 0; j < 28; j++) {
                const intensity = imageData[i][j];
                const gray = Math.floor((1 - intensity) * 255);
                ctx.fillStyle = `rgb(${gray}, ${gray}, ${gray})`;
                ctx.fillRect(j * cellWidth, i * cellHeight, cellWidth, cellHeight);
            }
        }
    }
    
    generateTestImage() {
        // Generate a random test image
        const randomDigit = Math.floor(Math.random() * 10);
        this.loadSampleImage(`digit${randomDigit}`);
        document.getElementById('imageSelect').value = `digit${randomDigit}`;
    }
    
    getImageFromCanvas() {
        const canvas = this.drawingCanvas;
        const ctx = this.drawingCtx;
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;
        
        // Convert to 28x28 grayscale
        const result = [];
        const scaleX = canvas.width / 28;
        const scaleY = canvas.height / 28;
        
        for (let i = 0; i < 28; i++) {
            result[i] = [];
            for (let j = 0; j < 28; j++) {
                const x = Math.floor(j * scaleX);
                const y = Math.floor(i * scaleY);
                const index = (y * canvas.width + x) * 4;
                const gray = (data[index] + data[index + 1] + data[index + 2]) / 3;
                result[i][j] = 1 - (gray / 255); // Invert for black on white
            }
        }
        
        return result;
    }
    
    classifyCurrentImage() {
        const imageData = this.getImageFromCanvas();
        const prediction = this.network.predict(imageData);
        
        this.displayClassificationResult(prediction);
        this.visualizer.animateForwardPass();
        
        console.log(`🔍 Image Classification Result:`);
        console.log(`   Predicted Digit: ${prediction.class}`);
        console.log(`   Confidence: ${prediction.confidence.toFixed(1)}%`);
    }
    
    displayClassificationResult(prediction) {
        // Update predicted digit
        document.getElementById('predictedDigit').textContent = prediction.class;
        
        // Update confidence
        document.getElementById('confidenceScore').textContent = prediction.confidence.toFixed(1) + '%';
        
        // Update probability distribution
        this.updateProbabilityBars(prediction.probabilities);
    }
    
    updateProbabilityBars(probabilities) {
        const container = document.getElementById('probabilityBars');
        container.innerHTML = '';
        
        for (let i = 0; i < probabilities.length; i++) {
            const probability = probabilities[i] * 100;
            
            const barDiv = document.createElement('div');
            barDiv.className = 'prob-bar';
            
            barDiv.innerHTML = `
                <span class="prob-label">${i}</span>
                <div class="prob-fill">
                    <div class="prob-value" style="width: ${probability}%"></div>
                </div>
                <span class="prob-percent">${probability.toFixed(1)}%</span>
            `;
            
            container.appendChild(barDiv);
        }
    }

    startTraining() {
        if (this.isTraining) {
            this.stopTraining();
            return;
        }

        this.isTraining = true;
        this.isPaused = false;
        document.getElementById('startCNNTraining').textContent = 'Stop Training';
        document.getElementById('pauseResumeCNN').textContent = 'Pause';

        this.trainStep();
    }

    trainStep() {
        if (!this.isTraining || this.isPaused) return;

        const result = this.network.simulateTraining();
        this.currentEpoch = result.epoch;

        this.updateVisualization();
        this.updateUI();

        if (this.currentEpoch >= this.maxEpochs) {
            this.stopTraining();
            return;
        }

        const speed = parseInt(document.getElementById('cnnAnimationSpeed').value);
        const delay = Math.max(50, 500 - speed * 50);

        this.trainingInterval = setTimeout(() => {
            this.trainStep();
        }, delay);
    }

    stopTraining() {
        this.isTraining = false;
        this.isPaused = false;
        document.getElementById('startCNNTraining').textContent = 'Start Training';
        document.getElementById('pauseResumeCNN').textContent = 'Pause';

        if (this.trainingInterval) {
            clearTimeout(this.trainingInterval);
            this.trainingInterval = null;
        }
    }

    togglePause() {
        if (!this.isTraining) return;

        this.isPaused = !this.isPaused;
        document.getElementById('pauseResumeCNN').textContent = this.isPaused ? 'Resume' : 'Pause';

        if (!this.isPaused) {
            this.trainStep();
        }
    }

    demonstrateForwardPass() {
        if (this.visualizer.isAnimating) return;

        this.classifyCurrentImage();
    }

    resetNetwork() {
        this.stopTraining();
        this.visualizer.stopAnimation();

        this.createNetwork();
        this.visualizer.setNetwork(this.network);

        this.updateVisualization();
        this.updateUI();
        this.generateTestImage();
    }

    updateParameterDisplay(id) {
        const element = document.getElementById(id);
        let displayId, value = element.value;

        switch (id) {
            case 'convFilters':
                displayId = 'filtersValue';
                break;
            case 'filterSize':
                displayId = 'filterSizeValue';
                value = `${value}x${value}`;
                break;
            case 'poolingSize':
                displayId = 'poolingSizeValue';
                value = `${value}x${value}`;
                break;
            case 'denseUnits':
                displayId = 'denseValue';
                break;
            case 'cnnLearningRate':
                displayId = 'cnnLrValue';
                break;
            case 'batchSize':
                displayId = 'batchValue';
                break;
            case 'cnnAnimationSpeed':
                displayId = 'cnnSpeedValue';
                break;
        }

        if (displayId) {
            document.getElementById(displayId).textContent = value;
        }
    }

    updateVisualization() {
        this.visualizer.updateNetworkState(this.network);
        this.visualizer.draw();
        this.visualizer.drawLossChart(this.network.lossHistory);
    }

    updateUI() {
        document.getElementById('cnnEpochCount').textContent = this.currentEpoch;

        const latestLoss = this.network.lossHistory.length > 0 ?
            this.network.lossHistory[this.network.lossHistory.length - 1] : 2.303;
        document.getElementById('cnnLossValue').textContent = latestLoss.toFixed(3);

        const latestAccuracy = this.network.accuracyHistory.length > 0 ?
            this.network.accuracyHistory[this.network.accuracyHistory.length - 1] : 10;
        document.getElementById('cnnAccuracyValue').textContent = latestAccuracy.toFixed(0) + '%';

        const valAccuracy = Math.min(100, latestAccuracy + (Math.random() - 0.5) * 5);
        document.getElementById('valAccuracyValue').textContent = valAccuracy.toFixed(0) + '%';

        // Update parameter displays
        ['convFilters', 'filterSize', 'poolingSize', 'denseUnits', 'cnnLearningRate', 'batchSize', 'cnnAnimationSpeed'].forEach(id => {
            this.updateParameterDisplay(id);
        });
    }
}

// Initialize the CNN app when the page loads
document.addEventListener('DOMContentLoaded', () => {
    const app = new CNNApp();

    // Make it globally accessible for debugging
    window.cnnApp = app;

    console.log('🔍 CNN Visualizer loaded!');
    console.log('Try drawing a digit or selecting a sample to see CNN classification in action!');
    console.log('Click "Start Training" to watch the CNN learn to recognize digits');
});
