<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Neural Network Visualizer</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🧠 Neural Network Visualizer</h1>
            <p>Watch how deep learning works with interactive animations!</p>
        </header>

        <div class="main-content">
            <div class="network-container">
                <canvas id="networkCanvas" width="800" height="500"></canvas>
                <div id="tooltip" class="tooltip">
                    <div class="tooltip-title"></div>
                    <div class="tooltip-content"></div>
                </div>

                <div class="control-group test-prediction-main">
                    <h3>🎯 Test Trained Model</h3>
                    <div class="prediction-test">
                        <div class="input-controls">
                            <label>
                                <span class="feature-name">Feature A (X₁):</span>
                                <input type="range" id="testInput1" min="0" max="1" step="0.1" value="0">
                                <span id="input1Value">0.0</span>
                            </label>
                            <label>
                                <span class="feature-name">Feature B (X₂):</span>
                                <input type="range" id="testInput2" min="0" max="1" step="0.1" value="0">
                                <span id="input2Value">0.0</span>
                            </label>
                            <button id="testPrediction">Test Prediction</button>
                        </div>
                        <div class="prediction-result">
                            <div class="prediction-output">
                                <div class="raw-output">
                                    Raw Output: <span id="rawOutput">0.000</span>
                                </div>
                                <div class="classified-output">
                                    <span class="classification-label">Predicted Class:</span>
                                    <span id="classifiedOutput" class="classification-value">Class 0</span>
                                </div>
                                <div class="target-labels">
                                    <div class="label-mapping">
                                        <span class="label-item">Class 0 = "Negative" (XOR = False)</span>
                                        <span class="label-item">Class 1 = "Positive" (XOR = True)</span>
                                    </div>
                                </div>
                                <div class="confidence-level">
                                    Confidence: <span id="confidenceLevel">50.0%</span>
                                </div>
                                <div class="decision-threshold">
                                    Decision: <span id="decisionReason">Below threshold (0.5)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="animation-controls">
                    <button id="startTraining">Start Training</button>
                    <button id="forwardPass">Forward Pass</button>
                    <button id="reset">Reset</button>
                    <button id="pauseResume">Pause</button>
                </div>
            </div>

            <div class="controls-panel">
                <div class="control-group">
                    <h3>Network Architecture</h3>
                    <label>
                        Input Neurons: 
                        <input type="range" id="inputNeurons" min="2" max="4" value="2">
                        <span id="inputValue">2</span>
                    </label>
                    <label>
                        Hidden Neurons: 
                        <input type="range" id="hiddenNeurons" min="2" max="8" value="4">
                        <span id="hiddenValue">4</span>
                    </label>
                    <label>
                        Output Neurons: 
                        <input type="range" id="outputNeurons" min="1" max="3" value="1">
                        <span id="outputValue">1</span>
                    </label>
                </div>

                <div class="control-group">
                    <h3>Training Parameters</h3>
                    <label>
                        Learning Rate: 
                        <input type="range" id="learningRate" min="0.01" max="1" step="0.01" value="0.1">
                        <span id="lrValue">0.1</span>
                    </label>
                    <label>
                        Animation Speed: 
                        <input type="range" id="animationSpeed" min="1" max="10" value="5">
                        <span id="speedValue">5</span>
                    </label>
                </div>

                <div class="control-group">
                    <h3>Training Data (XOR Problem)</h3>
                    <div class="data-display">
                        <div class="data-header">
                            <span class="feature-header">Feature A (X₁)</span>
                            <span class="feature-header">Feature B (X₂)</span>
                            <span class="target-header">Target Label</span>
                        </div>
                        <div class="data-point">
                            <span class="feature-value">0</span>
                            <span class="feature-value">0</span>
                            <span class="target-value">Class 0 (Negative)</span>
                        </div>
                        <div class="data-point">
                            <span class="feature-value">0</span>
                            <span class="feature-value">1</span>
                            <span class="target-value">Class 1 (Positive)</span>
                        </div>
                        <div class="data-point">
                            <span class="feature-value">1</span>
                            <span class="feature-value">0</span>
                            <span class="target-value">Class 1 (Positive)</span>
                        </div>
                        <div class="data-point">
                            <span class="feature-value">1</span>
                            <span class="feature-value">1</span>
                            <span class="target-value">Class 0 (Negative)</span>
                        </div>
                    </div>
                </div>

                <div class="control-group">
                    <h3>Training Progress</h3>
                    <div class="progress-info">
                        <div>Epoch: <span id="epochCount">0</span></div>
                        <div>Loss: <span id="lossValue">1.000</span></div>
                        <div>Accuracy: <span id="accuracyValue">25%</span></div>
                    </div>
                    <div class="loss-chart">
                        <canvas id="lossChart" width="300" height="150"></canvas>
                    </div>
                </div>

            </div>
        </div>

        <div class="info-panel">
            <h3>How It Works</h3>
            <div class="info-content">
                <p><strong>Forward Pass:</strong> Data flows from input → hidden → output layers</p>
                <p><strong>Activation:</strong> Each neuron applies an activation function (sigmoid)</p>
                <p><strong>Training:</strong> Weights are adjusted using backpropagation</p>
                <p><strong>XOR Problem:</strong> A classic non-linearly separable problem</p>
                <p><strong>🧮 Hover over neurons</strong> to see step-by-step mathematical calculations!</p>
                <p><strong>⚖️ Hover over connections</strong> to see weight values and their contributions!</p>
                <p><strong>🎯 Test predictions</strong> with custom inputs and see binary classification!</p>
                <p><strong>📊 Watch values update</strong> in real-time during training!</p>
            </div>
        </div>
    </div>

    <script src="neuralNetwork.js"></script>
    <script src="visualization.js"></script>
    <script src="app.js"></script>
</body>
</html>
