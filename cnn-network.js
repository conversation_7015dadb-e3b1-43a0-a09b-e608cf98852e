class ConvolutionalNeuralNetwork {
    constructor(config = {}) {
        this.config = {
            inputShape: [28, 28, 1],
            convFilters: config.convFilters || 8,
            filterSize: config.filterSize || 3,
            poolingSize: config.poolingSize || 2,
            denseUnits: config.denseUnits || 64,
            numClasses: 10,
            learningRate: config.learningRate || 0.01
        };
        
        this.initializeLayers();
        this.epoch = 0;
        this.lossHistory = [];
        this.accuracyHistory = [];
        
        // Store activations for visualization
        this.layerOutputs = {};
        this.filters = {};
    }
    
    initializeLayers() {
        const { inputShape, convFilters, filterSize, poolingSize, denseUnits, numClasses } = this.config;
        
        // Convolutional layer filters
        this.convFilters = this.initializeFilters(convFilters, filterSize, inputShape[2]);
        
        // Calculate dimensions after conv and pooling
        const convOutputSize = inputShape[0] - filterSize + 1;
        const poolOutputSize = Math.floor(convOutputSize / poolingSize);
        const flattenedSize = poolOutputSize * poolOutputSize * convFilters;
        
        // Dense layer weights
        this.denseWeights = this.randomMatrix(flattenedSize, denseUnits);
        this.denseBias = this.randomMatrix(1, denseUnits);
        
        // Output layer weights
        this.outputWeights = this.randomMatrix(denseUnits, numClasses);
        this.outputBias = this.randomMatrix(1, numClasses);
        
        this.dimensions = {
            convOutput: [convOutputSize, convOutputSize, convFilters],
            poolOutput: [poolOutputSize, poolOutputSize, convFilters],
            flattenedSize: flattenedSize
        };
    }
    
    initializeFilters(numFilters, filterSize, inputChannels) {
        const filters = [];
        for (let i = 0; i < numFilters; i++) {
            const filter = [];
            for (let j = 0; j < filterSize; j++) {
                filter[j] = [];
                for (let k = 0; k < filterSize; k++) {
                    filter[j][k] = [];
                    for (let c = 0; c < inputChannels; c++) {
                        filter[j][k][c] = (Math.random() - 0.5) * 0.1;
                    }
                }
            }
            filters.push(filter);
        }
        return filters;
    }
    
    randomMatrix(rows, cols) {
        const matrix = [];
        for (let i = 0; i < rows; i++) {
            matrix[i] = [];
            for (let j = 0; j < cols; j++) {
                matrix[i][j] = (Math.random() - 0.5) * 0.1;
            }
        }
        return matrix;
    }
    
    relu(x) {
        return Math.max(0, x);
    }
    
    softmax(arr) {
        const max = Math.max(...arr);
        const exp = arr.map(x => Math.exp(x - max));
        const sum = exp.reduce((a, b) => a + b, 0);
        return exp.map(x => x / sum);
    }
    
    convolution(input, filter) {
        if (!input || !input.length || !filter || !filter.length) {
            console.error('Invalid input or filter for convolution');
            return [];
        }

        const inputHeight = input.length;
        const inputWidth = input[0] ? input[0].length : 0;
        const filterSize = filter.length;

        if (inputWidth === 0 || filterSize === 0) {
            console.error('Invalid dimensions for convolution');
            return [];
        }

        const outputHeight = Math.max(1, inputHeight - filterSize + 1);
        const outputWidth = Math.max(1, inputWidth - filterSize + 1);

        const output = [];
        for (let i = 0; i < outputHeight; i++) {
            output[i] = [];
            for (let j = 0; j < outputWidth; j++) {
                let sum = 0;
                for (let fi = 0; fi < filterSize; fi++) {
                    for (let fj = 0; fj < filterSize; fj++) {
                        const inputRow = input[i + fi];
                        if (inputRow) {
                            const inputVal = inputRow[j + fj];
                            if (inputVal !== undefined) {
                                // Handle both scalar and array values
                                const val = Array.isArray(inputVal) ? inputVal[0] || 0 : inputVal;
                                const filterVal = filter[fi] && filter[fi][fj] && filter[fi][fj][0] !== undefined ?
                                                filter[fi][fj][0] : 0.1;
                                sum += val * filterVal;
                            }
                        }
                    }
                }
                output[i][j] = this.relu(sum);
            }
        }
        return output;
    }
    
    maxPooling(input, poolSize = 2) {
        if (!input || !input.length || !input[0]) {
            console.error('Invalid input for max pooling');
            return [];
        }

        const inputHeight = input.length;
        const inputWidth = input[0].length;
        const outputHeight = Math.floor(inputHeight / poolSize);
        const outputWidth = Math.floor(inputWidth / poolSize);

        const output = [];
        for (let i = 0; i < outputHeight; i++) {
            output[i] = [];
            for (let j = 0; j < outputWidth; j++) {
                let max = -Infinity;
                for (let pi = 0; pi < poolSize; pi++) {
                    for (let pj = 0; pj < poolSize; pj++) {
                        const rowIndex = i * poolSize + pi;
                        const colIndex = j * poolSize + pj;

                        if (input[rowIndex] && input[rowIndex][colIndex] !== undefined) {
                            const val = input[rowIndex][colIndex];
                            if (val > max) max = val;
                        }
                    }
                }
                output[i][j] = max === -Infinity ? 0 : max;
            }
        }
        return output;
    }
    
    flatten(input) {
        const flattened = [];

        // Handle different input formats
        if (Array.isArray(input) && input.length > 0) {
            if (Array.isArray(input[0]) && Array.isArray(input[0][0])) {
                // 3D array format (feature maps)
                for (let i = 0; i < input.length; i++) {
                    for (let j = 0; j < input[i].length; j++) {
                        for (let k = 0; k < input[i][j].length; k++) {
                            flattened.push(input[i][j][k] || 0);
                        }
                    }
                }
            } else if (Array.isArray(input[0])) {
                // 2D array format
                for (let i = 0; i < input.length; i++) {
                    for (let j = 0; j < input[i].length; j++) {
                        flattened.push(input[i][j] || 0);
                    }
                }
            } else {
                // 1D array format
                return input.slice();
            }
        }

        return flattened;
    }
    
    matrixMultiply(a, b) {
        const result = [];
        for (let i = 0; i < a.length; i++) {
            result[i] = [];
            for (let j = 0; j < b[0].length; j++) {
                let sum = 0;
                for (let k = 0; k < b.length; k++) {
                    sum += a[i][k] * b[k][j];
                }
                result[i][j] = sum;
            }
        }
        return result;
    }
    
    forward(input) {
        // Store input
        this.layerOutputs.input = input;
        
        // Convolutional layer
        const convOutputs = [];
        for (let f = 0; f < this.convFilters.length; f++) {
            const featureMap = this.convolution(input, this.convFilters[f]);
            convOutputs.push(featureMap);
        }
        this.layerOutputs.conv = convOutputs;
        
        // Pooling layer
        const poolOutputs = [];
        for (let f = 0; f < convOutputs.length; f++) {
            const pooled = this.maxPooling(convOutputs[f], this.config.poolingSize);
            poolOutputs.push(pooled);
        }
        this.layerOutputs.pool = poolOutputs;
        
        // Flatten
        const flattened = this.flatten(poolOutputs);
        this.layerOutputs.flatten = flattened;
        
        // Dense layer
        const denseInput = [flattened];
        const denseOutput = this.matrixMultiply(denseInput, this.denseWeights);
        for (let i = 0; i < denseOutput[0].length; i++) {
            denseOutput[0][i] = this.relu(denseOutput[0][i] + this.denseBias[0][i]);
        }
        this.layerOutputs.dense = denseOutput[0];
        
        // Output layer
        const outputInput = [denseOutput[0]];
        const outputRaw = this.matrixMultiply(outputInput, this.outputWeights);
        for (let i = 0; i < outputRaw[0].length; i++) {
            outputRaw[0][i] += this.outputBias[0][i];
        }
        
        // Softmax
        const output = this.softmax(outputRaw[0]);
        this.layerOutputs.output = output;
        
        return output;
    }
    
    predict(input) {
        const output = this.forward(input);
        const predictedClass = output.indexOf(Math.max(...output));
        const confidence = Math.max(...output) * 100;
        
        return {
            class: predictedClass,
            confidence: confidence,
            probabilities: output
        };
    }
    
    // Simplified training simulation
    simulateTraining() {
        this.epoch++;
        
        // Simulate loss decrease and accuracy increase
        const progress = Math.min(this.epoch / 100, 1);
        const loss = 2.303 * Math.exp(-progress * 3) + 0.1 * Math.random();
        const accuracy = 10 + (90 * progress) + (5 * Math.random() - 2.5);
        
        this.lossHistory.push(loss);
        this.accuracyHistory.push(Math.min(100, Math.max(0, accuracy)));
        
        return {
            epoch: this.epoch,
            loss: loss,
            accuracy: Math.min(100, Math.max(0, accuracy))
        };
    }
    
    getLayerInfo(layerName, index = 0) {
        const output = this.layerOutputs[layerName];
        if (!output) return null;
        
        switch (layerName) {
            case 'conv':
                return {
                    type: 'Convolutional',
                    shape: `${output[index].length}×${output[index][0].length}`,
                    filters: output.length,
                    activation: 'ReLU'
                };
            case 'pool':
                return {
                    type: 'Max Pooling',
                    shape: `${output[index].length}×${output[index][0].length}`,
                    poolSize: `${this.config.poolingSize}×${this.config.poolingSize}`
                };
            case 'dense':
                return {
                    type: 'Dense (Fully Connected)',
                    units: output.length,
                    activation: 'ReLU'
                };
            case 'output':
                return {
                    type: 'Output (Classification)',
                    units: output.length,
                    activation: 'Softmax'
                };
            default:
                return null;
        }
    }
}
