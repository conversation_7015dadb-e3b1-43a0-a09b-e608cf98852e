<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CNN Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        canvas {
            border: 2px solid #333;
            margin: 10px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .controls {
            margin: 20px 0;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 CNN Test - Simple Version</h1>
        
        <div class="controls">
            <h3>Test Controls</h3>
            <button id="testBtn1">Test Button 1</button>
            <button id="testBtn2">Test Button 2</button>
            <button id="testBtn3">Test Button 3</button>
            <button id="clearBtn">Clear Canvas</button>
        </div>
        
        <div>
            <h3>Drawing Canvas</h3>
            <canvas id="testCanvas" width="200" height="200"></canvas>
        </div>
        
        <div>
            <h3>Image Selection</h3>
            <select id="testSelect">
                <option value="0">Digit 0</option>
                <option value="1">Digit 1</option>
                <option value="2">Digit 2</option>
                <option value="3">Digit 3</option>
            </select>
        </div>
        
        <div class="result">
            <h3>Test Results</h3>
            <div id="testOutput">Ready for testing...</div>
        </div>
    </div>

    <script>
        console.log('🚀 Starting CNN Test...');
        
        // Test basic DOM elements
        function testDOM() {
            console.log('🔍 Testing DOM elements...');
            
            const elements = [
                'testBtn1', 'testBtn2', 'testBtn3', 'clearBtn',
                'testCanvas', 'testSelect', 'testOutput'
            ];
            
            let allFound = true;
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    console.log(`✅ Found element: ${id}`);
                } else {
                    console.error(`❌ Missing element: ${id}`);
                    allFound = false;
                }
            });
            
            return allFound;
        }
        
        // Test canvas drawing
        function testCanvas() {
            console.log('🎨 Testing canvas...');
            
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Draw test pattern
            ctx.fillStyle = 'blue';
            ctx.fillRect(50, 50, 100, 100);
            
            console.log('✅ Canvas drawing test complete');
        }
        
        // Test event listeners
        function setupEventListeners() {
            console.log('🔗 Setting up event listeners...');
            
            document.getElementById('testBtn1').addEventListener('click', () => {
                console.log('🎯 Test Button 1 clicked');
                document.getElementById('testOutput').textContent = 'Button 1 clicked!';
            });
            
            document.getElementById('testBtn2').addEventListener('click', () => {
                console.log('🎯 Test Button 2 clicked');
                document.getElementById('testOutput').textContent = 'Button 2 clicked!';
            });
            
            document.getElementById('testBtn3').addEventListener('click', () => {
                console.log('🎯 Test Button 3 clicked');
                document.getElementById('testOutput').textContent = 'Button 3 clicked!';
            });
            
            document.getElementById('clearBtn').addEventListener('click', () => {
                console.log('🧹 Clear button clicked');
                testCanvas();
                document.getElementById('testOutput').textContent = 'Canvas cleared!';
            });
            
            document.getElementById('testSelect').addEventListener('change', (e) => {
                console.log('📋 Select changed:', e.target.value);
                document.getElementById('testOutput').textContent = `Selected: ${e.target.value}`;
            });
            
            // Canvas drawing
            const canvas = document.getElementById('testCanvas');
            let isDrawing = false;
            
            canvas.addEventListener('mousedown', (e) => {
                isDrawing = true;
                draw(e);
            });
            
            canvas.addEventListener('mousemove', (e) => {
                if (isDrawing) draw(e);
            });
            
            canvas.addEventListener('mouseup', () => {
                isDrawing = false;
            });
            
            function draw(e) {
                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                const ctx = canvas.getContext('2d');
                ctx.fillStyle = 'black';
                ctx.beginPath();
                ctx.arc(x, y, 5, 0, 2 * Math.PI);
                ctx.fill();
            }
            
            console.log('✅ Event listeners setup complete');
        }
        
        // Initialize test
        document.addEventListener('DOMContentLoaded', () => {
            console.log('📄 DOM loaded, starting tests...');
            
            if (testDOM()) {
                console.log('✅ All DOM elements found');
                setupEventListeners();
                testCanvas();
                
                document.getElementById('testOutput').textContent = 'All tests passed! Try clicking buttons and drawing.';
                console.log('🎉 Test initialization complete!');
            } else {
                console.error('❌ Some DOM elements missing');
                document.getElementById('testOutput').textContent = 'Error: Some elements missing!';
            }
        });
    </script>
</body>
</html>
