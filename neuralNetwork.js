class NeuralNetwork {
    constructor(inputSize, hiddenSize, outputSize, learningRate = 0.1) {
        this.inputSize = inputSize;
        this.hiddenSize = hiddenSize;
        this.outputSize = outputSize;
        this.learningRate = learningRate;
        
        // Initialize weights randomly
        this.weightsInputHidden = this.randomMatrix(inputSize, hiddenSize);
        this.weightsHiddenOutput = this.randomMatrix(hiddenSize, outputSize);
        
        // Initialize biases
        this.biasHidden = this.randomMatrix(1, hiddenSize);
        this.biasOutput = this.randomMatrix(1, outputSize);
        
        // Store activations for visualization
        this.inputActivations = [];
        this.hiddenActivations = [];
        this.outputActivations = [];
        
        // Training history
        this.lossHistory = [];
        this.epoch = 0;
    }
    
    randomMatrix(rows, cols) {
        const matrix = [];
        // Xavier/Glorot initialization for better convergence
        const limit = Math.sqrt(6 / (rows + cols));

        for (let i = 0; i < rows; i++) {
            matrix[i] = [];
            for (let j = 0; j < cols; j++) {
                matrix[i][j] = (Math.random() * 2 - 1) * limit;
            }
        }
        return matrix;
    }
    
    sigmoid(x) {
        return 1 / (1 + Math.exp(-x));
    }
    
    sigmoidDerivative(x) {
        return x * (1 - x);
    }
    
    matrixMultiply(a, b) {
        const result = [];
        for (let i = 0; i < a.length; i++) {
            result[i] = [];
            for (let j = 0; j < b[0].length; j++) {
                let sum = 0;
                for (let k = 0; k < b.length; k++) {
                    sum += a[i][k] * b[k][j];
                }
                result[i][j] = sum;
            }
        }
        return result;
    }
    
    matrixAdd(a, b) {
        const result = [];
        for (let i = 0; i < a.length; i++) {
            result[i] = [];
            for (let j = 0; j < a[0].length; j++) {
                result[i][j] = a[i][j] + b[i][j];
            }
        }
        return result;
    }
    
    matrixSubtract(a, b) {
        const result = [];
        for (let i = 0; i < a.length; i++) {
            result[i] = [];
            for (let j = 0; j < a[0].length; j++) {
                result[i][j] = a[i][j] - b[i][j];
            }
        }
        return result;
    }
    
    applyFunction(matrix, func) {
        return matrix.map(row => row.map(func));
    }
    
    forward(input) {
        // Convert input to matrix format
        this.inputActivations = [input];
        
        // Hidden layer
        const hiddenInput = this.matrixAdd(
            this.matrixMultiply(this.inputActivations, this.weightsInputHidden),
            this.biasHidden
        );
        this.hiddenActivations = this.applyFunction(hiddenInput, this.sigmoid);
        
        // Output layer
        const outputInput = this.matrixAdd(
            this.matrixMultiply(this.hiddenActivations, this.weightsHiddenOutput),
            this.biasOutput
        );
        this.outputActivations = this.applyFunction(outputInput, this.sigmoid);
        
        return this.outputActivations[0];
    }
    
    backward(input, target) {
        const output = this.forward(input);
        
        // Calculate output layer error
        const outputError = this.matrixSubtract([target], this.outputActivations);
        const outputDelta = this.matrixMultiply(
            outputError,
            this.applyFunction(this.outputActivations, this.sigmoidDerivative)
        );
        
        // Calculate hidden layer error
        const hiddenError = this.matrixMultiply(
            outputDelta,
            this.transpose(this.weightsHiddenOutput)
        );
        const hiddenDelta = this.matrixMultiply(
            hiddenError,
            this.applyFunction(this.hiddenActivations, this.sigmoidDerivative)
        );
        
        // Update weights and biases
        this.weightsHiddenOutput = this.matrixAdd(
            this.weightsHiddenOutput,
            this.matrixMultiply(
                this.transpose(this.hiddenActivations),
                this.scalarMultiply(outputDelta, this.learningRate)
            )
        );
        
        this.weightsInputHidden = this.matrixAdd(
            this.weightsInputHidden,
            this.matrixMultiply(
                this.transpose(this.inputActivations),
                this.scalarMultiply(hiddenDelta, this.learningRate)
            )
        );
        
        this.biasOutput = this.matrixAdd(
            this.biasOutput,
            this.scalarMultiply(outputDelta, this.learningRate)
        );
        
        this.biasHidden = this.matrixAdd(
            this.biasHidden,
            this.scalarMultiply(hiddenDelta, this.learningRate)
        );
        
        return output;
    }
    
    transpose(matrix) {
        return matrix[0].map((_, colIndex) => matrix.map(row => row[colIndex]));
    }
    
    scalarMultiply(matrix, scalar) {
        return matrix.map(row => row.map(val => val * scalar));
    }
    
    calculateLoss(predictions, targets) {
        let totalLoss = 0;
        for (let i = 0; i < predictions.length; i++) {
            totalLoss += Math.pow(targets[i] - predictions[i], 2);
        }
        return totalLoss / predictions.length;
    }
    
    train(trainingData, epochs = 1000) {
        for (let epoch = 0; epoch < epochs; epoch++) {
            let totalLoss = 0;
            
            for (const data of trainingData) {
                const output = this.backward(data.input, data.output);
                totalLoss += this.calculateLoss(output, data.output);
            }
            
            const avgLoss = totalLoss / trainingData.length;
            this.lossHistory.push(avgLoss);
            this.epoch = epoch + 1;
            
            // Early stopping if loss is very small
            if (avgLoss < 0.001) break;
        }
    }
    
    predict(input) {
        return this.forward(input);
    }
    
    getAccuracy(trainingData) {
        let correct = 0;
        for (const data of trainingData) {
            const prediction = this.predict(data.input);
            const predicted = prediction[0] > 0.5 ? 1 : 0;
            const actual = data.output[0];
            if (predicted === actual) correct++;
        }
        return (correct / trainingData.length) * 100;
    }
}
