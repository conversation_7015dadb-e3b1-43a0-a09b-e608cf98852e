<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CNN Visualizer</title>
    <link rel="stylesheet" href="cnn-styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🔍 CNN Visualizer</h1>
            <p>Watch how Convolutional Neural Networks process images with interactive animations!</p>
        </header>

        <div class="main-content">
            <div class="cnn-container">
                <canvas id="cnnCanvas" width="1000" height="600"></canvas>
                <div id="cnnTooltip" class="tooltip">
                    <div class="tooltip-title"></div>
                    <div class="tooltip-content"></div>
                </div>
                
                <div class="control-group test-prediction-main">
                    <h3>🖼️ Test Image Classification</h3>
                    <div class="image-test">
                        <div class="input-controls">
                            <div class="image-selector">
                                <label>Select Test Image:</label>
                                <select id="imageSelect">
                                    <option value="digit0">Digit 0</option>
                                    <option value="digit1">Digit 1</option>
                                    <option value="digit2">Digit 2</option>
                                    <option value="digit3">Digit 3</option>
                                    <option value="digit4">Digit 4</option>
                                    <option value="digit5">Digit 5</option>
                                    <option value="digit6">Digit 6</option>
                                    <option value="digit7">Digit 7</option>
                                    <option value="digit8">Digit 8</option>
                                    <option value="digit9">Digit 9</option>
                                </select>
                            </div>
                            <div class="drawing-area">
                                <label>Or Draw Your Own:</label>
                                <canvas id="drawingCanvas" width="140" height="140"></canvas>
                                <button id="clearDrawing">Clear</button>
                            </div>
                            <button id="classifyImage">Classify Image</button>
                        </div>
                        <div class="classification-result">
                            <div class="prediction-output">
                                <div class="predicted-class">
                                    <span class="classification-label">Predicted Digit:</span>
                                    <span id="predictedDigit" class="classification-value">-</span>
                                </div>
                                <div class="confidence-score">
                                    Confidence: <span id="confidenceScore">0.0%</span>
                                </div>
                                <div class="probability-distribution">
                                    <h4>Probability Distribution:</h4>
                                    <div id="probabilityBars" class="probability-bars"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="animation-controls">
                    <button id="startCNNTraining">Start Training</button>
                    <button id="forwardPassCNN">Forward Pass</button>
                    <button id="resetCNN">Reset</button>
                    <button id="pauseResumeCNN">Pause</button>
                </div>
            </div>

            <div class="controls-panel">
                <div class="control-group">
                    <h3>CNN Architecture</h3>
                    <label>
                        Conv Filters: 
                        <input type="range" id="convFilters" min="4" max="16" value="8">
                        <span id="filtersValue">8</span>
                    </label>
                    <label>
                        Filter Size: 
                        <input type="range" id="filterSize" min="3" max="7" step="2" value="3">
                        <span id="filterSizeValue">3x3</span>
                    </label>
                    <label>
                        Pooling Size: 
                        <input type="range" id="poolingSize" min="2" max="4" value="2">
                        <span id="poolingSizeValue">2x2</span>
                    </label>
                    <label>
                        Dense Units: 
                        <input type="range" id="denseUnits" min="32" max="128" step="16" value="64">
                        <span id="denseValue">64</span>
                    </label>
                </div>

                <div class="control-group">
                    <h3>Training Parameters</h3>
                    <label>
                        Learning Rate: 
                        <input type="range" id="cnnLearningRate" min="0.001" max="0.1" step="0.001" value="0.01">
                        <span id="cnnLrValue">0.01</span>
                    </label>
                    <label>
                        Batch Size: 
                        <input type="range" id="batchSize" min="16" max="128" step="16" value="32">
                        <span id="batchValue">32</span>
                    </label>
                    <label>
                        Animation Speed: 
                        <input type="range" id="cnnAnimationSpeed" min="1" max="10" value="5">
                        <span id="cnnSpeedValue">5</span>
                    </label>
                </div>

                <div class="control-group">
                    <h3>Dataset (MNIST Digits)</h3>
                    <div class="data-display">
                        <div class="data-point">Training Images: 60,000</div>
                        <div class="data-point">Test Images: 10,000</div>
                        <div class="data-point">Image Size: 28×28 pixels</div>
                        <div class="data-point">Classes: 0-9 digits</div>
                    </div>
                </div>

                <div class="control-group">
                    <h3>Training Progress</h3>
                    <div class="progress-info">
                        <div>Epoch: <span id="cnnEpochCount">0</span></div>
                        <div>Loss: <span id="cnnLossValue">2.303</span></div>
                        <div>Accuracy: <span id="cnnAccuracyValue">10%</span></div>
                        <div>Val Accuracy: <span id="valAccuracyValue">10%</span></div>
                    </div>
                    <div class="loss-chart">
                        <canvas id="cnnLossChart" width="300" height="150"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="info-panel">
            <h3>How CNNs Work</h3>
            <div class="info-content">
                <p><strong>Convolution:</strong> Filters detect features like edges and patterns</p>
                <p><strong>Pooling:</strong> Reduces spatial dimensions while keeping important features</p>
                <p><strong>Feature Maps:</strong> Each filter creates a feature map highlighting specific patterns</p>
                <p><strong>Fully Connected:</strong> Final layers classify based on extracted features</p>
                <p><strong>🔍 Hover over layers</strong> to see detailed convolution mathematics!</p>
                <p><strong>🖼️ Draw digits</strong> to test the trained model in real-time!</p>
                <p><strong>📊 Watch feature extraction</strong> as data flows through the network!</p>
            </div>
        </div>
    </div>

    <script src="cnn-network.js"></script>
    <script src="cnn-visualization.js"></script>
    <script src="cnn-app.js"></script>
</body>
</html>
