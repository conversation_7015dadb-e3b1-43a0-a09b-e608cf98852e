* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    min-height: 100vh;
}

.container {
    max-width: 1600px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.main-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.cnn-container {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    position: relative;
}

#cnnCanvas {
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    background: #fafafa;
    display: block;
    margin: 0 auto;
    cursor: crosshair;
}

/* Test Image Classification */
.test-prediction-main {
    margin: 20px 0 10px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.test-prediction-main h3 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 1.1rem;
    text-align: center;
}

.image-test {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.input-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.image-selector select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.drawing-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

#drawingCanvas {
    border: 2px solid #ddd;
    border-radius: 8px;
    background: white;
    cursor: crosshair;
}

.input-controls button {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.input-controls button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.classification-result {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e0e0e0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.predicted-class {
    text-align: center;
    margin-bottom: 10px;
    font-size: 1.2rem;
    font-weight: bold;
}

.classification-value {
    font-size: 2rem;
    padding: 8px 16px;
    border-radius: 8px;
    margin-left: 8px;
    background: #e8f5e8;
    color: #2e7d32;
    border: 2px solid #2e7d32;
}

.confidence-score {
    text-align: center;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 15px;
}

.probability-distribution h4 {
    font-size: 0.9rem;
    margin-bottom: 8px;
    color: #666;
}

.probability-bars {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.prob-bar {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.8rem;
}

.prob-label {
    width: 20px;
    font-weight: bold;
}

.prob-fill {
    flex: 1;
    height: 16px;
    background: #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
}

.prob-value {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 8px;
    transition: width 0.3s ease;
}

.prob-percent {
    width: 40px;
    text-align: right;
    font-size: 0.7rem;
    color: #666;
}

.animation-controls {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
}

.animation-controls button {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.animation-controls button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

.controls-panel {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    height: fit-content;
}

.control-group {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
}

.control-group:last-child {
    border-bottom: none;
}

.control-group h3 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.control-group label {
    display: block;
    margin-bottom: 10px;
    font-weight: 500;
}

.control-group input[type="range"] {
    width: 100%;
    margin: 5px 0;
    accent-color: #667eea;
}

.control-group span {
    color: #667eea;
    font-weight: bold;
}

.data-display {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.data-point {
    margin: 5px 0;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.progress-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
}

.progress-info div {
    margin: 5px 0;
    font-weight: 500;
}

.loss-chart {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: white;
}

.info-panel {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.info-panel h3 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.info-content p {
    margin-bottom: 10px;
    line-height: 1.6;
}

.info-content strong {
    color: #667eea;
}

/* Tooltip styles */
.tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.95);
    color: white;
    padding: 12px 16px;
    border-radius: 10px;
    font-size: 12px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    pointer-events: none;
    z-index: 1000;
    max-width: 400px;
    min-width: 280px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
    opacity: 0;
    transition: opacity 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.tooltip.visible {
    opacity: 1;
}

.tooltip-title {
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
    border-bottom: 1px solid #667eea;
    padding-bottom: 3px;
}

.tooltip-content {
    line-height: 1.4;
}

/* Responsive design */
@media (max-width: 1400px) {
    .main-content {
        grid-template-columns: 1fr;
    }
    
    #cnnCanvas {
        width: 100%;
        height: auto;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .image-test {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .animation-controls {
        flex-wrap: wrap;
    }
    
    .animation-controls button {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}
