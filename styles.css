* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.main-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.network-container {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

#networkCanvas {
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    background: #fafafa;
    display: block;
    margin: 0 auto;
}

.animation-controls {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
}

.animation-controls button {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.animation-controls button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

.animation-controls button:active {
    transform: translateY(0);
}

.controls-panel {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    height: fit-content;
}

.control-group {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
}

.control-group:last-child {
    border-bottom: none;
}

.control-group h3 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.control-group label {
    display: block;
    margin-bottom: 10px;
    font-weight: 500;
}

.control-group input[type="range"] {
    width: 100%;
    margin: 5px 0;
    accent-color: #667eea;
}

.control-group span {
    color: #667eea;
    font-weight: bold;
}

.data-display {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.data-header {
    display: grid;
    grid-template-columns: 1fr 1fr 2fr;
    gap: 15px;
    padding: 10px 0;
    border-bottom: 2px solid #667eea;
    margin-bottom: 10px;
    font-weight: bold;
    color: #667eea;
}

.feature-header, .target-header {
    text-align: center;
    font-size: 0.9rem;
}

.data-point {
    display: grid;
    grid-template-columns: 1fr 1fr 2fr;
    gap: 15px;
    padding: 8px 0;
    border-bottom: 1px solid #e0e0e0;
    font-family: 'Courier New', monospace;
    align-items: center;
}

.data-point:last-child {
    border-bottom: none;
}

.feature-value {
    text-align: center;
    font-weight: bold;
    color: #333;
    background: #e3f2fd;
    padding: 4px 8px;
    border-radius: 4px;
}

.target-value {
    text-align: center;
    font-weight: bold;
    color: #2e7d32;
    background: #e8f5e8;
    padding: 4px 8px;
    border-radius: 4px;
}

.feature-name {
    font-weight: bold;
    color: #667eea;
    margin-right: 8px;
    display: inline-block;
    min-width: 120px;
}

.target-labels {
    margin: 10px 0;
    padding: 10px;
    background: #f0f4ff;
    border-radius: 6px;
    border-left: 4px solid #667eea;
}

.label-mapping {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.label-item {
    font-size: 0.85rem;
    color: #555;
    font-weight: 500;
}

/* Training Status Styles */
.training-status {
    margin: 15px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
    font-family: 'Courier New', monospace;
    transition: all 0.3s ease;
}

.training-header {
    font-weight: bold;
    color: #667eea;
    margin-bottom: 10px;
    font-size: 1rem;
    text-align: center;
}

.training-details {
    display: grid;
    gap: 8px;
}

.training-input {
    color: #333;
    font-weight: bold;
}

.training-expected {
    color: #2e7d32;
    font-weight: 500;
}

.training-predicted {
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.training-predicted.correct {
    color: #2e7d32;
    background: #e8f5e8;
}

.training-predicted.incorrect {
    color: #c62828;
    background: #ffebee;
}

.training-raw {
    color: #666;
    font-size: 0.9rem;
    font-style: italic;
}

/* Prediction Test Styles */
.prediction-test {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.input-controls {
    margin-bottom: 15px;
}

.input-controls label {
    display: block;
    margin-bottom: 8px;
}

.input-controls button {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: bold;
    margin-top: 10px;
    transition: all 0.3s ease;
}

.input-controls button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.prediction-result {
    background: white;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

.prediction-output {
    font-family: 'Courier New', monospace;
}

.prediction-output > div {
    margin: 6px 0;
    padding: 4px 0;
}

.raw-output {
    color: #666;
    font-size: 0.9rem;
}

.classified-output {
    font-size: 1.2rem;
    font-weight: bold;
    padding: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    margin: 8px 0;
}

.classification-label {
    color: #333;
}

.classification-value {
    font-size: 1.5rem;
    padding: 4px 8px;
    border-radius: 4px;
    margin-left: 8px;
}

.classification-value.class-0 {
    background: #ffebee;
    color: #c62828;
    border: 2px solid #c62828;
}

.classification-value.class-1 {
    background: #e8f5e8;
    color: #2e7d32;
    border: 2px solid #2e7d32;
}

.confidence-level {
    font-weight: bold;
    color: #667eea;
}

.confidence-high {
    color: #2e7d32;
}

.confidence-medium {
    color: #f57c00;
}

.confidence-low {
    color: #c62828;
}

.decision-threshold {
    font-size: 0.9rem;
    color: #666;
    font-style: italic;
}

.progress-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
}

.progress-info div {
    margin: 5px 0;
    font-weight: 500;
}

.loss-chart {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: white;
}

.info-panel {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.info-panel h3 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.info-content p {
    margin-bottom: 10px;
    line-height: 1.6;
}

.info-content strong {
    color: #667eea;
}

/* Responsive design */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 1fr;
    }
    
    #networkCanvas {
        width: 100%;
        height: auto;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    header h1 {
        font-size: 2rem;
    }

    .animation-controls {
        flex-wrap: wrap;
    }

    .animation-controls button {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    .test-prediction-main .input-controls {
        flex-direction: column;
        gap: 10px;
    }

    .test-prediction-main .input-controls label {
        width: 100%;
        justify-content: space-between;
    }

    .test-prediction-main .prediction-output {
        grid-template-columns: 1fr;
        gap: 8px;
    }
}

/* Animation classes */
.neuron-active {
    animation: pulse 0.5s ease-in-out;
}

.connection-active {
    animation: flow 1s ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

@keyframes flow {
    0% { opacity: 0.3; }
    50% { opacity: 1; }
    100% { opacity: 0.3; }
}

/* Tooltip styles */
.tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.95);
    color: white;
    padding: 12px 16px;
    border-radius: 10px;
    font-size: 12px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    pointer-events: none;
    z-index: 1000;
    max-width: 350px;
    min-width: 250px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
    opacity: 0;
    transition: opacity 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.tooltip.visible {
    opacity: 1;
}

.tooltip-title {
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
    border-bottom: 1px solid #667eea;
    padding-bottom: 3px;
}

.tooltip-content {
    line-height: 1.4;
}

.tooltip-value {
    color: #4CAF50;
    font-weight: bold;
}

.tooltip-negative {
    color: #f44336;
}

.math-step {
    background: rgba(255, 255, 255, 0.1);
    padding: 6px 8px;
    margin: 4px 0;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    border-left: 3px solid #667eea;
}

.math-formula {
    font-family: 'Courier New', monospace;
    background: rgba(102, 126, 234, 0.2);
    padding: 4px 6px;
    border-radius: 3px;
    margin: 2px 0;
    font-weight: bold;
}

.calculation-section {
    margin: 6px 0;
    padding: 4px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.calculation-section:first-child {
    border-top: none;
}

.step-title {
    font-weight: bold;
    color: #667eea;
    margin-bottom: 3px;
}

.step-result {
    border-top: 1px solid #666;
    margin: 3px 0;
    padding-top: 3px;
    font-weight: bold;
}

.network-container {
    position: relative;
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* Test Prediction in main container */
.test-prediction-main {
    margin: 20px 0 10px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.test-prediction-main h3 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 1.1rem;
    text-align: center;
}

.test-prediction-main .prediction-test {
    background: transparent;
    padding: 0;
    border: none;
}

.test-prediction-main .input-controls {
    display: flex;
    gap: 20px;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.test-prediction-main .input-controls label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    margin-bottom: 0;
}

.test-prediction-main .input-controls input[type="range"] {
    width: 120px;
}

.test-prediction-main .input-controls button {
    margin-top: 0;
    padding: 10px 20px;
}

.test-prediction-main .prediction-result {
    background: white;
    border-radius: 8px;
    padding: 12px;
    border: 1px solid #e0e0e0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.test-prediction-main .prediction-output {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    font-size: 0.9rem;
}

.test-prediction-main .classified-output {
    grid-column: 1 / -1;
    text-align: center;
    margin: 8px 0;
}

#networkCanvas {
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    background: #fafafa;
    display: block;
    margin: 0 auto;
    cursor: crosshair;
}
