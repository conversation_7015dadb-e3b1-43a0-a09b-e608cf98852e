class NetworkVisualizer {
    constructor(canvasId, lossChartId) {
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.lossCanvas = document.getElementById(lossChartId);
        this.lossCtx = this.lossCanvas.getContext('2d');

        this.animationSpeed = 5;
        this.isAnimating = false;
        this.animationFrame = null;

        // Network layout
        this.layers = [];
        this.connections = [];
        this.activeNeurons = new Set();
        this.activeConnections = new Set();

        // Animation state
        this.currentDataPoint = 0;
        this.animationStep = 0;
        this.maxAnimationSteps = 60;

        // Tooltip functionality
        this.tooltip = document.getElementById('tooltip');
        this.tooltipTitle = this.tooltip.querySelector('.tooltip-title');
        this.tooltipContent = this.tooltip.querySelector('.tooltip-content');
        this.network = null;

        this.setupTooltipEvents();
    }

    setupTooltipEvents() {
        this.canvas.addEventListener('mousemove', (e) => {
            this.handleMouseMove(e);
        });

        this.canvas.addEventListener('mouseleave', () => {
            this.hideTooltip();
        });
    }

    handleMouseMove(e) {
        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        // Check if hovering over a neuron
        const hoveredNeuron = this.getNeuronAt(x, y);
        if (hoveredNeuron) {
            this.showNeuronTooltip(hoveredNeuron, e.clientX, e.clientY);
            return;
        }

        // Check if hovering over a connection
        const hoveredConnection = this.getConnectionAt(x, y);
        if (hoveredConnection) {
            this.showConnectionTooltip(hoveredConnection, e.clientX, e.clientY);
            return;
        }

        this.hideTooltip();
    }

    getNeuronAt(x, y) {
        for (let layerIndex = 0; layerIndex < this.layers.length; layerIndex++) {
            for (let neuronIndex = 0; neuronIndex < this.layers[layerIndex].length; neuronIndex++) {
                const neuron = this.layers[layerIndex][neuronIndex];
                const distance = Math.sqrt((x - neuron.x) ** 2 + (y - neuron.y) ** 2);
                if (distance <= neuron.radius) {
                    return { ...neuron, layerIndex, neuronIndex };
                }
            }
        }
        return null;
    }

    getConnectionAt(x, y) {
        const tolerance = 5;

        for (let i = 0; i < this.connections.length; i++) {
            const connection = this.connections[i];
            const distance = this.distanceToLine(x, y, connection.from.x, connection.from.y, connection.to.x, connection.to.y);

            if (distance <= tolerance) {
                return { ...connection, index: i };
            }
        }
        return null;
    }

    distanceToLine(px, py, x1, y1, x2, y2) {
        const A = px - x1;
        const B = py - y1;
        const C = x2 - x1;
        const D = y2 - y1;

        const dot = A * C + B * D;
        const lenSq = C * C + D * D;

        if (lenSq === 0) return Math.sqrt(A * A + B * B);

        let param = dot / lenSq;

        if (param < 0) {
            return Math.sqrt(A * A + B * B);
        } else if (param > 1) {
            const E = px - x2;
            const F = py - y2;
            return Math.sqrt(E * E + F * F);
        } else {
            const projX = x1 + param * C;
            const projY = y1 + param * D;
            const dx = px - projX;
            const dy = py - projY;
            return Math.sqrt(dx * dx + dy * dy);
        }
    }

    setupNetwork(inputSize, hiddenSize, outputSize) {
        this.layers = [];
        this.connections = [];
        
        const layerSpacing = this.canvas.width / 4;
        const startX = layerSpacing;
        
        // Input layer
        const inputLayer = this.createLayer(inputSize, startX, 'input');
        this.layers.push(inputLayer);
        
        // Hidden layer
        const hiddenLayer = this.createLayer(hiddenSize, startX + layerSpacing, 'hidden');
        this.layers.push(hiddenLayer);
        
        // Output layer
        const outputLayer = this.createLayer(outputSize, startX + 2 * layerSpacing, 'output');
        this.layers.push(outputLayer);
        
        // Create connections
        this.createConnections(inputLayer, hiddenLayer);
        this.createConnections(hiddenLayer, outputLayer);
    }
    
    createLayer(size, x, type) {
        const layer = [];
        const spacing = this.canvas.height / (size + 1);
        
        for (let i = 0; i < size; i++) {
            const y = spacing * (i + 1);
            layer.push({
                x: x,
                y: y,
                type: type,
                activation: 0,
                radius: 25
            });
        }
        
        return layer;
    }
    
    createConnections(fromLayer, toLayer) {
        for (const fromNeuron of fromLayer) {
            for (const toNeuron of toLayer) {
                this.connections.push({
                    from: fromNeuron,
                    to: toNeuron,
                    weight: Math.random() * 2 - 1,
                    active: false
                });
            }
        }
    }
    
    showNeuronTooltip(neuron, clientX, clientY) {
        const layerNames = ['Input Features', 'Hidden Layer', 'Target Classes'];
        const layerName = layerNames[neuron.layerIndex] || 'Unknown';

        // Get specific names for input and output neurons
        let neuronName = '';
        if (neuron.layerIndex === 0) {
            const featureNames = ['Feature A (X₁)', 'Feature B (X₂)', 'Feature C (X₃)', 'Feature D (X₄)'];
            neuronName = featureNames[neuron.neuronIndex] || `Feature ${neuron.neuronIndex + 1}`;
        } else if (neuron.layerIndex === 2) {
            const classNames = ['Class 0 (Negative)', 'Class 1 (Positive)', 'Class 2'];
            neuronName = classNames[neuron.neuronIndex] || `Class ${neuron.neuronIndex}`;
        } else {
            neuronName = `Hidden Neuron ${neuron.neuronIndex + 1}`;
        }

        this.tooltipTitle.textContent = neuronName;

        let content = `<div>Final Activation: <span class="tooltip-value ${neuron.activation < 0 ? 'tooltip-negative' : ''}">${neuron.activation.toFixed(4)}</span></div>`;
        content += `<div style="margin: 8px 0; border-top: 1px solid #444; padding-top: 8px;"><strong>Mathematical Calculation:</strong></div>`;

        if (neuron.layerIndex === 0) {
            content += `<div>Type: Input Feature</div>`;
            content += `<div>Function: Direct feature value</div>`;
            content += `<div style="font-family: monospace; background: rgba(255,255,255,0.1); padding: 4px; margin: 4px 0;">`;
            content += `output = feature_value = ${neuron.activation.toFixed(4)}`;
            content += `</div>`;
        } else {
            content += `<div>Type: Processing Node</div>`;
            content += `<div>Function: σ(weighted_sum + bias)</div>`;

            // Calculate the mathematical steps
            const calculation = this.calculateNeuronMath(neuron);

            content += `<div class="math-step">`;
            content += `<div class="step-title">Step 1: Weighted Sum</div>`;

            for (let i = 0; i < calculation.inputs.length; i++) {
                const weight = calculation.weights[i];
                const input = calculation.inputs[i];
                const product = weight * input;
                const weightClass = weight < 0 ? 'tooltip-negative' : 'tooltip-value';
                const inputClass = input < 0 ? 'tooltip-negative' : 'tooltip-value';
                const productClass = product < 0 ? 'tooltip-negative' : 'tooltip-value';
                content += `<div><span class="${weightClass}">${weight.toFixed(3)}</span> × <span class="${inputClass}">${input.toFixed(3)}</span> = <span class="${productClass}">${product.toFixed(3)}</span></div>`;
            }

            content += `<div class="step-result">`;
            content += `Weighted Sum = <span class="tooltip-value ${calculation.weightedSum < 0 ? 'tooltip-negative' : ''}">${calculation.weightedSum.toFixed(4)}</span></div>`;
            content += `</div>`;

            content += `<div class="math-step">`;
            content += `<div class="step-title">Step 2: Add Bias</div>`;
            content += `<div class="math-formula">${calculation.weightedSum.toFixed(4)} + ${calculation.bias.toFixed(4)} = ${calculation.preActivation.toFixed(4)}</div>`;
            content += `<div class="step-result">Pre-activation = <span class="tooltip-value ${calculation.preActivation < 0 ? 'tooltip-negative' : ''}">${calculation.preActivation.toFixed(4)}</span></div>`;
            content += `</div>`;

            content += `<div class="math-step">`;
            content += `<div class="step-title">Step 3: Sigmoid Activation</div>`;
            content += `<div class="math-formula">σ(x) = 1/(1+e^(-x))</div>`;
            content += `<div>σ(${calculation.preActivation.toFixed(4)}) = 1/(1+e^(-${calculation.preActivation.toFixed(4)}))</div>`;
            content += `<div class="step-result">Final Output = <span class="tooltip-value">${calculation.finalActivation.toFixed(4)}</span></div>`;
            content += `</div>`;

            // Add classification decision for output neurons
            if (neuron.layerIndex === 2) {
                const threshold = 0.5;
                const classified = calculation.finalActivation >= threshold ? 1 : 0;
                const confidence = classified === 1 ?
                    calculation.finalActivation * 100 :
                    (1 - calculation.finalActivation) * 100;

                content += `<div class="math-step">`;
                content += `<div class="step-title">Step 4: Binary Classification</div>`;
                content += `<div class="math-formula">if output ≥ 0.5 then Class 1, else Class 0</div>`;
                content += `<div>${calculation.finalActivation.toFixed(4)} ${classified === 1 ? '≥' : '<'} 0.5</div>`;
                content += `<div class="step-result">Classification = <span class="tooltip-value ${classified === 1 ? '' : 'tooltip-negative'}">${classified}</span></div>`;
                content += `<div class="step-result">Confidence = <span class="tooltip-value">${confidence.toFixed(1)}%</span></div>`;
                content += `</div>`;
            }

            if (calculation.bias !== undefined) {
                content += `<div>Bias: <span class="tooltip-value ${calculation.bias < 0 ? 'tooltip-negative' : ''}">${calculation.bias.toFixed(4)}</span></div>`;
            }
        }

        this.tooltipContent.innerHTML = content;
        this.showTooltip(clientX, clientY);
    }

    showConnectionTooltip(connection, clientX, clientY) {
        const fromLayerNames = ['Input', 'Hidden', 'Output'];
        const toLayerNames = ['Hidden', 'Output', 'Output'];

        // Determine which layers this connection connects
        let fromLayer = -1, toLayer = -1;
        let fromIndex = -1, toIndex = -1;

        // Find the connection details
        for (let i = 0; i < this.layers.length - 1; i++) {
            for (let j = 0; j < this.layers[i].length; j++) {
                for (let k = 0; k < this.layers[i + 1].length; k++) {
                    if (this.layers[i][j] === connection.from && this.layers[i + 1][k] === connection.to) {
                        fromLayer = i;
                        toLayer = i + 1;
                        fromIndex = j;
                        toIndex = k;
                        break;
                    }
                }
            }
        }

        this.tooltipTitle.textContent = `Connection Weight`;

        let content = `<div>From: <strong>${fromLayerNames[fromLayer]} Neuron ${fromIndex + 1}</strong></div>`;
        content += `<div>To: <strong>${toLayerNames[toLayer]} Neuron ${toIndex + 1}</strong></div>`;
        content += `<div style="margin: 8px 0; border-top: 1px solid #444; padding-top: 8px;"><strong>Weight Properties:</strong></div>`;
        content += `<div>Value: <span class="tooltip-value ${connection.weight < 0 ? 'tooltip-negative' : ''}">${connection.weight.toFixed(6)}</span></div>`;
        content += `<div>Magnitude: <span class="tooltip-value">${Math.abs(connection.weight).toFixed(6)}</span></div>`;
        content += `<div>Strength: <strong>${Math.abs(connection.weight) > 0.5 ? 'Strong' : Math.abs(connection.weight) > 0.1 ? 'Medium' : 'Weak'}</strong></div>`;
        content += `<div>Effect: <strong>${connection.weight > 0 ? 'Excitatory (+)' : 'Inhibitory (-)'}</strong></div>`;

        // Add mathematical contribution if we have the input value
        if (fromLayer >= 0 && this.layers[fromLayer] && this.layers[fromLayer][fromIndex]) {
            const inputValue = this.layers[fromLayer][fromIndex].activation;
            const contribution = connection.weight * inputValue;
            content += `<div class="math-step">`;
            content += `<div class="step-title">Current Contribution:</div>`;
            content += `<div class="math-formula">weight × input = contribution</div>`;
            content += `<div><span class="tooltip-value ${connection.weight < 0 ? 'tooltip-negative' : ''}">${connection.weight.toFixed(4)}</span> × <span class="tooltip-value ${inputValue < 0 ? 'tooltip-negative' : ''}">${inputValue.toFixed(4)}</span> = <span class="tooltip-value ${contribution < 0 ? 'tooltip-negative' : ''}">${contribution.toFixed(4)}</span></div>`;
            content += `</div>`;
        }

        this.tooltipContent.innerHTML = content;
        this.showTooltip(clientX, clientY);
    }

    showTooltip(clientX, clientY) {
        this.tooltip.style.left = (clientX + 10) + 'px';
        this.tooltip.style.top = (clientY - 10) + 'px';
        this.tooltip.classList.add('visible');
    }

    hideTooltip() {
        this.tooltip.classList.remove('visible');
    }

    calculateNeuronMath(neuron) {
        if (!this.network || neuron.layerIndex === 0) {
            return null;
        }

        const result = {
            inputs: [],
            weights: [],
            weightedSum: 0,
            bias: 0,
            preActivation: 0,
            finalActivation: neuron.activation
        };

        // Get the previous layer
        const prevLayer = this.layers[neuron.layerIndex - 1];

        // Get inputs from previous layer
        for (let i = 0; i < prevLayer.length; i++) {
            result.inputs.push(prevLayer[i].activation);
        }

        // Get weights connecting to this neuron
        if (neuron.layerIndex === 1) {
            // Hidden layer - get weights from input layer
            for (let i = 0; i < this.network.weightsInputHidden.length; i++) {
                result.weights.push(this.network.weightsInputHidden[i][neuron.neuronIndex]);
            }
            result.bias = this.network.biasHidden[0][neuron.neuronIndex];
        } else if (neuron.layerIndex === 2) {
            // Output layer - get weights from hidden layer
            for (let i = 0; i < this.network.weightsHiddenOutput.length; i++) {
                result.weights.push(this.network.weightsHiddenOutput[i][neuron.neuronIndex]);
            }
            result.bias = this.network.biasOutput[0][neuron.neuronIndex];
        }

        // Calculate weighted sum
        for (let i = 0; i < result.inputs.length; i++) {
            result.weightedSum += result.inputs[i] * result.weights[i];
        }

        // Add bias
        result.preActivation = result.weightedSum + result.bias;

        // Apply sigmoid activation
        result.finalActivation = 1 / (1 + Math.exp(-result.preActivation));

        return result;
    }

    setNetwork(network) {
        this.network = network;
    }

    updateNetworkState(network) {
        this.network = network;
        let outputChanged = false;

        // Update neuron activations
        if (network.inputActivations.length > 0) {
            for (let i = 0; i < this.layers[0].length; i++) {
                this.layers[0][i].activation = network.inputActivations[0][i] || 0;
            }
        }

        if (network.hiddenActivations.length > 0) {
            for (let i = 0; i < this.layers[1].length; i++) {
                this.layers[1][i].activation = network.hiddenActivations[0][i] || 0;
            }
        }

        if (network.outputActivations.length > 0) {
            for (let i = 0; i < this.layers[2].length; i++) {
                const oldActivation = this.layers[2][i].activation;
                const newActivation = network.outputActivations[0][i] || 0;

                // Check if output classification would change
                const oldClass = oldActivation >= 0.5 ? 1 : 0;
                const newClass = newActivation >= 0.5 ? 1 : 0;

                if (oldClass !== newClass) {
                    outputChanged = true;
                }

                this.layers[2][i].activation = newActivation;
            }
        }

        // If output classification changed, trigger visual feedback
        if (outputChanged && !this.isAnimating) {
            this.animateClassLabelChange();
        }
        
        // Update connection weights
        let connectionIndex = 0;
        
        // Input to hidden connections
        for (let i = 0; i < network.weightsInputHidden.length; i++) {
            for (let j = 0; j < network.weightsInputHidden[i].length; j++) {
                if (connectionIndex < this.connections.length) {
                    this.connections[connectionIndex].weight = network.weightsInputHidden[i][j];
                    connectionIndex++;
                }
            }
        }
        
        // Hidden to output connections
        for (let i = 0; i < network.weightsHiddenOutput.length; i++) {
            for (let j = 0; j < network.weightsHiddenOutput[i].length; j++) {
                if (connectionIndex < this.connections.length) {
                    this.connections[connectionIndex].weight = network.weightsHiddenOutput[i][j];
                    connectionIndex++;
                }
            }
        }
    }
    
    draw() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Draw connections
        this.drawConnections();
        
        // Draw neurons
        this.drawNeurons();
        
        // Draw labels
        this.drawLabels();
    }
    
    drawConnections() {
        for (const connection of this.connections) {
            const opacity = Math.abs(connection.weight);
            const color = connection.weight > 0 ? 'rgba(76, 175, 80, ' : 'rgba(244, 67, 54, ';
            
            this.ctx.strokeStyle = color + Math.min(opacity, 1) + ')';
            this.ctx.lineWidth = Math.abs(connection.weight) * 3 + 1;
            
            if (connection.active) {
                this.ctx.lineWidth += 2;
                this.ctx.strokeStyle = 'rgba(255, 193, 7, 0.8)';
            }
            
            this.ctx.beginPath();
            this.ctx.moveTo(connection.from.x, connection.from.y);
            this.ctx.lineTo(connection.to.x, connection.to.y);
            this.ctx.stroke();
        }
    }
    
    drawNeurons() {
        for (const layer of this.layers) {
            for (const neuron of layer) {
                // Neuron circle
                const intensity = Math.abs(neuron.activation);
                const color = neuron.activation > 0 ? 
                    `rgba(76, 175, 80, ${0.3 + intensity * 0.7})` : 
                    `rgba(244, 67, 54, ${0.3 + intensity * 0.7})`;
                
                this.ctx.fillStyle = color;
                this.ctx.beginPath();
                this.ctx.arc(neuron.x, neuron.y, neuron.radius, 0, 2 * Math.PI);
                this.ctx.fill();
                
                // Neuron border
                this.ctx.strokeStyle = this.activeNeurons.has(neuron) ? 
                    'rgba(255, 193, 7, 1)' : 'rgba(0, 0, 0, 0.3)';
                this.ctx.lineWidth = this.activeNeurons.has(neuron) ? 3 : 1;
                this.ctx.stroke();
                
                // Activation value
                this.ctx.fillStyle = 'black';
                this.ctx.font = '12px Arial';
                this.ctx.textAlign = 'center';
                this.ctx.fillText(
                    neuron.activation.toFixed(2), 
                    neuron.x, 
                    neuron.y + 4
                );
            }
        }
    }
    
    drawLabels() {
        this.ctx.fillStyle = 'black';
        this.ctx.font = 'bold 16px Arial';
        this.ctx.textAlign = 'center';

        // Layer labels
        const layerSpacing = this.canvas.width / 4;
        this.ctx.fillText('Input Features', layerSpacing, 30);
        this.ctx.fillText('Hidden Layer', layerSpacing * 2, 30);
        this.ctx.fillText('Target Classes', layerSpacing * 3, 30);

        // Feature names for input neurons
        this.ctx.font = 'bold 12px Arial';
        this.ctx.fillStyle = '#667eea';
        if (this.layers[0]) {
            const featureNames = ['Feature A (X₁)', 'Feature B (X₂)', 'Feature C (X₃)', 'Feature D (X₄)'];
            for (let i = 0; i < this.layers[0].length; i++) {
                const neuron = this.layers[0][i];
                if (featureNames[i]) {
                    this.ctx.fillText(featureNames[i], neuron.x - 80, neuron.y + 4);
                }
            }
        }

        // Dynamic target class labels for output neurons
        if (this.layers[2]) {
            for (let i = 0; i < this.layers[2].length; i++) {
                const neuron = this.layers[2][i];

                // Determine current classification based on neuron activation
                const threshold = 0.5;
                const isActive = neuron.activation >= threshold;

                // Dynamic class label based on current activation
                let currentLabel;
                if (i === 0) {
                    // For single output neuron (binary classification)
                    currentLabel = isActive ? 'Class 1\n(Positive)' : 'Class 0\n(Negative)';
                } else {
                    // For multi-class scenarios
                    currentLabel = `Class ${i}`;
                }

                // Color coding based on activation level
                const confidence = Math.abs(neuron.activation - 0.5) * 2; // 0 to 1
                if (isActive) {
                    this.ctx.fillStyle = `rgba(46, 125, 50, ${0.6 + confidence * 0.4})`; // Green with confidence
                } else {
                    this.ctx.fillStyle = `rgba(198, 40, 40, ${0.6 + confidence * 0.4})`; // Red with confidence
                }

                // Draw dynamic label with background
                const lines = currentLabel.split('\n');
                const labelX = neuron.x + 80;
                const labelY = neuron.y - 5;

                // Background rectangle for better visibility
                this.ctx.fillRect(labelX - 45, labelY - 15, 90, lines.length * 15 + 5);

                // Text with contrasting color
                this.ctx.fillStyle = 'white';
                this.ctx.font = 'bold 11px Arial';
                this.ctx.fillText(lines[0], labelX, labelY);
                if (lines[1]) {
                    this.ctx.fillText(lines[1], labelX, labelY + 15);
                }

                // Add confidence indicator
                this.ctx.font = '9px Arial';
                this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                this.ctx.fillText(`${(confidence * 100).toFixed(0)}%`, labelX, labelY + (lines.length * 15) + 5);
            }
        }
    }
    
    animateForwardPass(network, inputData) {
        if (this.isAnimating) return;
        
        this.isAnimating = true;
        this.animationStep = 0;
        
        const animate = () => {
            this.activeNeurons.clear();
            this.activeConnections.clear();
            
            const progress = this.animationStep / this.maxAnimationSteps;
            
            if (progress < 0.33) {
                // Activate input layer
                for (const neuron of this.layers[0]) {
                    this.activeNeurons.add(neuron);
                }
            } else if (progress < 0.66) {
                // Activate connections to hidden layer
                const hiddenConnections = this.connections.slice(0, this.layers[0].length * this.layers[1].length);
                for (const connection of hiddenConnections) {
                    this.activeConnections.add(connection);
                    connection.active = true;
                }
                
                // Activate hidden layer
                for (const neuron of this.layers[1]) {
                    this.activeNeurons.add(neuron);
                }
            } else {
                // Activate connections to output layer
                const outputConnections = this.connections.slice(this.layers[0].length * this.layers[1].length);
                for (const connection of outputConnections) {
                    this.activeConnections.add(connection);
                    connection.active = true;
                }
                
                // Activate output layer
                for (const neuron of this.layers[2]) {
                    this.activeNeurons.add(neuron);
                }

                // Trigger class label animation when output layer is reached
                this.animateClassLabelChange();
            }
            
            this.draw();
            
            this.animationStep++;
            if (this.animationStep < this.maxAnimationSteps) {
                this.animationFrame = requestAnimationFrame(animate);
            } else {
                this.isAnimating = false;
                // Reset active states
                for (const connection of this.connections) {
                    connection.active = false;
                }
            }
        };
        
        // Update network state first
        network.forward(inputData);
        this.updateNetworkState(network);
        
        animate();
    }

    animateClassLabelChange() {
        // Add visual emphasis to class label changes
        if (this.layers[2] && this.layers[2].length > 0) {
            const outputNeuron = this.layers[2][0];

            // Create a temporary highlight effect around the class label area
            const labelX = outputNeuron.x + 80;
            const labelY = outputNeuron.y - 5;

            // Store original properties
            const originalFillStyle = this.ctx.fillStyle;

            // Draw pulsing highlight effect
            let pulseIntensity = 0;
            const pulseAnimation = () => {
                pulseIntensity += 0.1;

                // Clear and redraw to show the pulse effect
                this.draw();

                // Add pulsing glow effect around class label
                this.ctx.save();
                this.ctx.globalAlpha = Math.sin(pulseIntensity) * 0.3 + 0.3;
                this.ctx.fillStyle = '#667eea';
                this.ctx.fillRect(labelX - 50, labelY - 20, 100, 40);
                this.ctx.restore();

                if (pulseIntensity < Math.PI * 2) {
                    requestAnimationFrame(pulseAnimation);
                } else {
                    // Restore original state
                    this.ctx.fillStyle = originalFillStyle;
                    this.draw();
                }
            };

            // Start the pulse animation
            requestAnimationFrame(pulseAnimation);
        }
    }

    drawLossChart(lossHistory) {
        this.lossCtx.clearRect(0, 0, this.lossCanvas.width, this.lossCanvas.height);
        
        if (lossHistory.length < 2) return;
        
        const maxLoss = Math.max(...lossHistory);
        const minLoss = Math.min(...lossHistory);
        const range = maxLoss - minLoss || 1;
        
        this.lossCtx.strokeStyle = '#667eea';
        this.lossCtx.lineWidth = 2;
        this.lossCtx.beginPath();
        
        for (let i = 0; i < lossHistory.length; i++) {
            const x = (i / (lossHistory.length - 1)) * this.lossCanvas.width;
            const y = this.lossCanvas.height - ((lossHistory[i] - minLoss) / range) * this.lossCanvas.height;
            
            if (i === 0) {
                this.lossCtx.moveTo(x, y);
            } else {
                this.lossCtx.lineTo(x, y);
            }
        }
        
        this.lossCtx.stroke();
        
        // Draw axes
        this.lossCtx.strokeStyle = '#ccc';
        this.lossCtx.lineWidth = 1;
        this.lossCtx.beginPath();
        this.lossCtx.moveTo(0, this.lossCanvas.height);
        this.lossCtx.lineTo(this.lossCanvas.width, this.lossCanvas.height);
        this.lossCtx.moveTo(0, 0);
        this.lossCtx.lineTo(0, this.lossCanvas.height);
        this.lossCtx.stroke();
    }
    
    stopAnimation() {
        this.isAnimating = false;
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
        }
    }
}
