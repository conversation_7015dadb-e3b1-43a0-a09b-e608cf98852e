<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CNN Visualizer - Working Version</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .main-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .cnn-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            position: relative;
        }

        #cnnCanvas {
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #fafafa;
            display: block;
            margin: 0 auto;
            cursor: crosshair;
        }

        .test-prediction-main {
            margin: 20px 0 10px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .test-prediction-main h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.1rem;
            text-align: center;
        }

        .image-test {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .input-controls {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .drawing-area {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        #drawingCanvas {
            border: 2px solid #ddd;
            border-radius: 8px;
            background: white;
            cursor: crosshair;
        }

        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .classification-result {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .predicted-class {
            text-align: center;
            margin-bottom: 10px;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .classification-value {
            font-size: 2rem;
            padding: 8px 16px;
            border-radius: 8px;
            margin-left: 8px;
            background: #e8f5e8;
            color: #2e7d32;
            border: 2px solid #2e7d32;
        }

        .animation-controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }

        .controls-panel {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            height: fit-content;
        }

        .control-group {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e0e0e0;
        }

        .control-group h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        select, input[type="range"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .progress-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .progress-info div {
            margin: 5px 0;
            font-weight: 500;
        }

        #probabilityBars {
            display: flex;
            flex-direction: column;
            gap: 3px;
        }

        .prob-bar {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.8rem;
        }

        .prob-label {
            width: 20px;
            font-weight: bold;
        }

        .prob-fill {
            flex: 1;
            height: 16px;
            background: #f0f0f0;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
        }

        .prob-value {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 8px;
            transition: width 0.3s ease;
        }

        .prob-percent {
            width: 40px;
            text-align: right;
            font-size: 0.7rem;
            color: #666;
        }

        /* Tooltip styles */
        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.95);
            color: white;
            padding: 12px 16px;
            border-radius: 10px;
            font-size: 12px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            pointer-events: none;
            z-index: 1000;
            max-width: 400px;
            min-width: 280px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
            opacity: 0;
            transition: opacity 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .tooltip.visible {
            opacity: 1;
        }

        .tooltip-title {
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
            border-bottom: 1px solid #667eea;
            padding-bottom: 3px;
        }

        .tooltip-content {
            line-height: 1.4;
        }

        .tooltip-value {
            color: #4CAF50;
            font-weight: bold;
        }

        .tooltip-negative {
            color: #f44336;
        }

        .math-step {
            background: rgba(255, 255, 255, 0.1);
            padding: 6px 8px;
            margin: 4px 0;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            border-left: 3px solid #667eea;
        }

        .math-formula {
            font-family: 'Courier New', monospace;
            background: rgba(102, 126, 234, 0.2);
            padding: 4px 6px;
            border-radius: 3px;
            margin: 2px 0;
            font-weight: bold;
        }

        .step-title {
            font-weight: bold;
            color: #667eea;
            margin-bottom: 3px;
        }

        .step-result {
            border-top: 1px solid #666;
            margin: 3px 0;
            padding-top: 3px;
            font-weight: bold;
        }

        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .image-test {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>🔍 CNN Visualizer</h1>
            <p>Interactive Convolutional Neural Network Demonstration</p>
        </header>

        <div class="main-content">
            <div class="cnn-container">
                <canvas id="cnnCanvas" width="800" height="400"></canvas>
                <div id="cnnTooltip" class="tooltip">
                    <div class="tooltip-title"></div>
                    <div class="tooltip-content"></div>
                </div>
                
                <div class="control-group test-prediction-main">
                    <h3>🖼️ Test Image Classification</h3>
                    <div class="image-test">
                        <div class="input-controls">
                            <div class="image-selector">
                                <label>Select Test Image:</label>
                                <select id="imageSelect">
                                    <option value="digit0">Digit 0</option>
                                    <option value="digit1">Digit 1</option>
                                    <option value="digit2">Digit 2</option>
                                    <option value="digit3">Digit 3</option>
                                    <option value="digit4">Digit 4</option>
                                    <option value="digit5">Digit 5</option>
                                    <option value="digit6">Digit 6</option>
                                    <option value="digit7">Digit 7</option>
                                    <option value="digit8">Digit 8</option>
                                    <option value="digit9">Digit 9</option>
                                </select>
                            </div>
                            <div class="drawing-area">
                                <label>Or Draw Your Own:</label>
                                <canvas id="drawingCanvas" width="140" height="140"></canvas>
                                <button id="clearDrawing">Clear</button>
                                <div style="margin-top: 10px;">
                                    <label style="font-size: 12px;">Network Input (28×28):</label>
                                    <canvas id="debugCanvas" width="84" height="84" style="border: 1px solid #ccc; image-rendering: pixelated;"></canvas>
                                </div>
                            </div>
                            <button id="classifyImage">Classify Image</button>
                        </div>
                        <div class="classification-result">
                            <div class="prediction-output">
                                <div class="predicted-class">
                                    <span class="classification-label">Predicted Digit:</span>
                                    <span id="predictedDigit" class="classification-value">-</span>
                                </div>
                                <div class="confidence-score">
                                    Confidence: <span id="confidenceScore">0.0%</span>
                                </div>
                                <div class="probability-distribution">
                                    <h4>Probability Distribution:</h4>
                                    <div id="probabilityBars" class="probability-bars"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="animation-controls">
                    <button id="startCNNTraining">Start Training</button>
                    <button id="forwardPassCNN">Forward Pass</button>
                    <button id="resetCNN">Reset</button>
                    <button id="pauseResumeCNN">Pause</button>
                </div>
            </div>

            <div class="controls-panel">
                <div class="control-group">
                    <h3>🎛️ Fine-tuning Parameters</h3>
                    <label>
                        Learning Rate:
                        <input type="range" id="learningRate" min="0.001" max="0.1" step="0.001" value="0.01">
                        <span id="lrValue">0.01</span>
                    </label>
                    <label>
                        Pattern Sensitivity:
                        <input type="range" id="patternSensitivity" min="0.1" max="2.0" step="0.1" value="1.0">
                        <span id="patternValue">1.0</span>
                    </label>
                    <label>
                        Feature Strength:
                        <input type="range" id="featureStrength" min="0.5" max="3.0" step="0.1" value="1.5">
                        <span id="featureValue">1.5</span>
                    </label>
                    <label>
                        Noise Reduction:
                        <input type="range" id="noiseReduction" min="0.0" max="1.0" step="0.1" value="0.3">
                        <span id="noiseValue">0.3</span>
                    </label>
                    <label>
                        Training Intensity:
                        <input type="range" id="trainingIntensity" min="0.1" max="2.0" step="0.1" value="1.0">
                        <span id="intensityValue">1.0</span>
                    </label>
                </div>

                <div class="control-group">
                    <h3>Training Progress</h3>
                    <div class="progress-info">
                        <div>Epoch: <span id="cnnEpochCount">0</span></div>
                        <div>Loss: <span id="cnnLossValue">2.303</span></div>
                        <div>Accuracy: <span id="cnnAccuracyValue">10%</span></div>
                        <div>Validation: <span id="valAccuracyValue">10%</span></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        console.log('🚀 Starting CNN Visualizer...');
        
        // Simple CNN implementation
        class SimpleCNN {
            constructor() {
                this.epoch = 0;
                this.lossHistory = [];
                this.isTraining = false;

                // Store layer outputs for visualization
                this.layerOutputs = {
                    input: null,
                    conv: null,
                    pool: null,
                    flatten: null,
                    dense: null,
                    output: null
                };

                // Store calculation details for tooltips
                this.calculations = {
                    convolution: [],
                    pooling: [],
                    dense: [],
                    softmax: []
                };

                // Network parameters
                this.config = {
                    inputShape: [28, 28],
                    convFilters: 8,
                    filterSize: 3,
                    poolSize: 2,
                    denseUnits: 64,
                    outputClasses: 10
                };

                // Fine-tuning parameters
                this.tuningParams = {
                    learningRate: 0.01,
                    patternSensitivity: 1.0,
                    featureStrength: 1.5,
                    noiseReduction: 0.3,
                    trainingIntensity: 1.0
                };

                // Training progress affects accuracy
                this.trainingProgress = 0; // 0 to 1
                this.baseAccuracy = 0.1; // Starting accuracy

                // Digit pattern templates for better recognition
                this.digitPatterns = this.initializeDigitPatterns();
            }

            initializeDigitPatterns() {
                return {
                    0: { // Circle pattern
                        center: true,
                        edges: true,
                        corners: false,
                        horizontal: false,
                        vertical: false,
                        diagonal: false
                    },
                    1: { // Vertical line
                        center: false,
                        edges: false,
                        corners: false,
                        horizontal: false,
                        vertical: true,
                        diagonal: false
                    },
                    2: { // Horizontal segments
                        center: false,
                        edges: true,
                        corners: true,
                        horizontal: true,
                        vertical: false,
                        diagonal: false
                    },
                    3: { // Curved segments
                        center: false,
                        edges: true,
                        corners: false,
                        horizontal: true,
                        vertical: false,
                        diagonal: false
                    },
                    4: { // Vertical + horizontal
                        center: false,
                        edges: false,
                        corners: true,
                        horizontal: true,
                        vertical: true,
                        diagonal: false
                    },
                    5: { // Mixed segments
                        center: false,
                        edges: true,
                        corners: true,
                        horizontal: true,
                        vertical: false,
                        diagonal: false
                    },
                    6: { // Curved with loop
                        center: true,
                        edges: true,
                        corners: false,
                        horizontal: true,
                        vertical: false,
                        diagonal: false
                    },
                    7: { // Diagonal line
                        center: false,
                        edges: false,
                        corners: true,
                        horizontal: true,
                        vertical: false,
                        diagonal: true
                    },
                    8: { // Double loop
                        center: true,
                        edges: true,
                        corners: false,
                        horizontal: true,
                        vertical: false,
                        diagonal: false
                    },
                    9: { // Loop with tail
                        center: true,
                        edges: true,
                        corners: false,
                        horizontal: false,
                        vertical: true,
                        diagonal: false
                    }
                };
            }
            
            predict(imageData) {
                // Store input
                this.layerOutputs.input = imageData;

                // Extract features from image
                const features = this.extractImageFeatures(imageData);

                // Simulate convolution layer
                const convOutputs = this.simulateConvolution(imageData, features);
                this.layerOutputs.conv = convOutputs;

                // Simulate pooling layer
                const poolOutputs = this.simulatePooling(convOutputs);
                this.layerOutputs.pool = poolOutputs;

                // Simulate flatten
                const flattened = this.simulateFlatten(poolOutputs);
                this.layerOutputs.flatten = flattened;

                // Simulate dense layer
                const denseOutputs = this.simulateDense(flattened);
                this.layerOutputs.dense = denseOutputs;

                // Intelligent pattern matching for output
                const outputProbs = this.intelligentClassification(features, denseOutputs);
                this.layerOutputs.output = outputProbs;

                const predictedClass = outputProbs.indexOf(Math.max(...outputProbs));
                const confidence = Math.max(...outputProbs) * 100;

                return {
                    class: predictedClass,
                    confidence: confidence,
                    probabilities: outputProbs
                };
            }

            extractImageFeatures(imageData) {
                const height = imageData.length;
                const width = imageData[0].length;

                // Advanced feature extraction
                const features = {
                    // Shape features
                    topDensity: 0,
                    middleDensity: 0,
                    bottomDensity: 0,
                    leftDensity: 0,
                    rightDensity: 0,
                    centerDensity: 0,

                    // Line features
                    horizontalLines: 0,
                    verticalLines: 0,
                    diagonalLines: 0,

                    // Structural features
                    loops: 0,
                    openings: 0,
                    endpoints: 0,
                    intersections: 0,

                    // Geometric features
                    aspectRatio: 0,
                    symmetryVertical: 0,
                    symmetryHorizontal: 0,
                    compactness: 0,

                    // Specific digit patterns
                    circularPattern: 0,
                    straightPattern: 0,
                    curvedPattern: 0
                };

                // Calculate region densities
                const regions = this.calculateRegionDensities(imageData);
                features.topDensity = regions.top;
                features.middleDensity = regions.middle;
                features.bottomDensity = regions.bottom;
                features.leftDensity = regions.left;
                features.rightDensity = regions.right;
                features.centerDensity = regions.center;

                // Detect lines and patterns
                const lineFeatures = this.detectLines(imageData);
                features.horizontalLines = lineFeatures.horizontal;
                features.verticalLines = lineFeatures.vertical;
                features.diagonalLines = lineFeatures.diagonal;

                // Detect structural elements
                const structural = this.detectStructuralFeatures(imageData);
                features.loops = structural.loops;
                features.openings = structural.openings;
                features.endpoints = structural.endpoints;
                features.intersections = structural.intersections;

                // Calculate geometric properties
                features.aspectRatio = this.calculateAspectRatio(imageData);
                features.symmetryVertical = this.calculateVerticalSymmetry(imageData);
                features.symmetryHorizontal = this.calculateHorizontalSymmetry(imageData);
                features.compactness = this.calculateCompactness(imageData);

                // Detect specific patterns
                features.circularPattern = this.detectCircularPattern(imageData);
                features.straightPattern = this.detectStraightPattern(imageData);
                features.curvedPattern = this.detectCurvedPattern(imageData);

                return features;
            }

            calculateRegionDensities(imageData) {
                const height = imageData.length;
                const width = imageData[0].length;

                let top = 0, middle = 0, bottom = 0;
                let left = 0, right = 0, center = 0;

                for (let i = 0; i < height; i++) {
                    for (let j = 0; j < width; j++) {
                        const pixel = imageData[i][j];

                        // Vertical regions
                        if (i < height / 3) top += pixel;
                        else if (i < 2 * height / 3) middle += pixel;
                        else bottom += pixel;

                        // Horizontal regions
                        if (j < width / 3) left += pixel;
                        else if (j < 2 * width / 3) center += pixel;
                        else right += pixel;
                    }
                }

                const totalArea = height * width / 3;
                return {
                    top: top / totalArea,
                    middle: middle / totalArea,
                    bottom: bottom / totalArea,
                    left: left / totalArea,
                    right: right / totalArea,
                    center: center / totalArea
                };
            }

            detectLines(imageData) {
                const height = imageData.length;
                const width = imageData[0].length;
                let horizontal = 0, vertical = 0, diagonal = 0;

                // Detect horizontal lines
                for (let i = 1; i < height - 1; i++) {
                    let lineStrength = 0;
                    for (let j = 1; j < width - 1; j++) {
                        if (imageData[i][j] > 0.3) {
                            lineStrength++;
                        }
                    }
                    if (lineStrength > width * 0.4) {
                        horizontal += lineStrength / width;
                    }
                }

                // Detect vertical lines
                for (let j = 1; j < width - 1; j++) {
                    let lineStrength = 0;
                    for (let i = 1; i < height - 1; i++) {
                        if (imageData[i][j] > 0.3) {
                            lineStrength++;
                        }
                    }
                    if (lineStrength > height * 0.4) {
                        vertical += lineStrength / height;
                    }
                }

                // Detect diagonal lines
                for (let offset = -10; offset <= 10; offset++) {
                    let diagStrength = 0;
                    let count = 0;
                    for (let i = 0; i < height; i++) {
                        const j = i + offset + width / 2 - height / 2;
                        if (j >= 0 && j < width) {
                            if (imageData[i][Math.floor(j)] > 0.3) {
                                diagStrength++;
                            }
                            count++;
                        }
                    }
                    if (count > 0 && diagStrength / count > 0.4) {
                        diagonal += diagStrength / count;
                    }
                }

                return { horizontal, vertical, diagonal };
            }

            detectStructuralFeatures(imageData) {
                const height = imageData.length;
                const width = imageData[0].length;
                let loops = 0, openings = 0, endpoints = 0, intersections = 0;

                // Simple loop detection (enclosed areas)
                for (let i = 2; i < height - 2; i++) {
                    for (let j = 2; j < width - 2; j++) {
                        if (imageData[i][j] < 0.2) { // Empty center
                            let surrounding = 0;
                            for (let di = -2; di <= 2; di++) {
                                for (let dj = -2; dj <= 2; dj++) {
                                    if (Math.abs(di) + Math.abs(dj) === 2) {
                                        if (imageData[i + di][j + dj] > 0.5) {
                                            surrounding++;
                                        }
                                    }
                                }
                            }
                            if (surrounding >= 6) loops += 0.1;
                        }
                    }
                }

                // Endpoint detection
                for (let i = 1; i < height - 1; i++) {
                    for (let j = 1; j < width - 1; j++) {
                        if (imageData[i][j] > 0.5) {
                            let neighbors = 0;
                            for (let di = -1; di <= 1; di++) {
                                for (let dj = -1; dj <= 1; dj++) {
                                    if (di !== 0 || dj !== 0) {
                                        if (imageData[i + di][j + dj] > 0.5) {
                                            neighbors++;
                                        }
                                    }
                                }
                            }
                            if (neighbors === 1) endpoints += 0.1;
                            if (neighbors >= 4) intersections += 0.1;
                        }
                    }
                }

                return { loops, openings, endpoints, intersections };
            }

            calculateAspectRatio(imageData) {
                const height = imageData.length;
                const width = imageData[0].length;

                let minRow = height, maxRow = 0, minCol = width, maxCol = 0;

                for (let i = 0; i < height; i++) {
                    for (let j = 0; j < width; j++) {
                        if (imageData[i][j] > 0.3) {
                            minRow = Math.min(minRow, i);
                            maxRow = Math.max(maxRow, i);
                            minCol = Math.min(minCol, j);
                            maxCol = Math.max(maxCol, j);
                        }
                    }
                }

                const objHeight = maxRow - minRow + 1;
                const objWidth = maxCol - minCol + 1;

                return objWidth / objHeight;
            }

            calculateVerticalSymmetry(imageData) {
                const height = imageData.length;
                const width = imageData[0].length;
                let symmetryScore = 0;
                let comparisons = 0;

                for (let i = 0; i < height; i++) {
                    for (let j = 0; j < width/2; j++) {
                        const left = imageData[i][j];
                        const right = imageData[i][width - 1 - j];
                        symmetryScore += 1 - Math.abs(left - right);
                        comparisons++;
                    }
                }

                return comparisons > 0 ? symmetryScore / comparisons : 0;
            }

            calculateHorizontalSymmetry(imageData) {
                const height = imageData.length;
                const width = imageData[0].length;
                let symmetryScore = 0;
                let comparisons = 0;

                for (let i = 0; i < height/2; i++) {
                    for (let j = 0; j < width; j++) {
                        const top = imageData[i][j];
                        const bottom = imageData[height - 1 - i][j];
                        symmetryScore += 1 - Math.abs(top - bottom);
                        comparisons++;
                    }
                }

                return comparisons > 0 ? symmetryScore / comparisons : 0;
            }

            calculateCompactness(imageData) {
                const height = imageData.length;
                const width = imageData[0].length;
                let totalPixels = 0;
                let boundingBoxArea = 0;

                let minRow = height, maxRow = 0, minCol = width, maxCol = 0;

                for (let i = 0; i < height; i++) {
                    for (let j = 0; j < width; j++) {
                        if (imageData[i][j] > 0.3) {
                            totalPixels++;
                            minRow = Math.min(minRow, i);
                            maxRow = Math.max(maxRow, i);
                            minCol = Math.min(minCol, j);
                            maxCol = Math.max(maxCol, j);
                        }
                    }
                }

                if (totalPixels > 0) {
                    boundingBoxArea = (maxRow - minRow + 1) * (maxCol - minCol + 1);
                    return totalPixels / boundingBoxArea;
                }

                return 0;
            }

            detectCircularPattern(imageData) {
                const height = imageData.length;
                const width = imageData[0].length;
                const centerX = width / 2;
                const centerY = height / 2;

                let circularScore = 0;
                let samples = 0;

                for (let radius = 3; radius < Math.min(width, height) / 3; radius++) {
                    let edgePixels = 0;
                    let totalSamples = 0;

                    for (let angle = 0; angle < 2 * Math.PI; angle += 0.2) {
                        const x = Math.round(centerX + radius * Math.cos(angle));
                        const y = Math.round(centerY + radius * Math.sin(angle));

                        if (x >= 0 && x < width && y >= 0 && y < height) {
                            if (imageData[y][x] > 0.5) edgePixels++;
                            totalSamples++;
                        }
                    }

                    if (totalSamples > 0) {
                        circularScore += edgePixels / totalSamples;
                        samples++;
                    }
                }

                return samples > 0 ? circularScore / samples : 0;
            }

            detectStraightPattern(imageData) {
                const lineFeatures = this.detectLines(imageData);
                return Math.max(lineFeatures.horizontal, lineFeatures.vertical, lineFeatures.diagonal);
            }

            detectCurvedPattern(imageData) {
                const height = imageData.length;
                const width = imageData[0].length;
                let curveScore = 0;
                let samples = 0;

                // Detect curves by looking for direction changes
                for (let i = 2; i < height - 2; i++) {
                    for (let j = 2; j < width - 2; j++) {
                        if (imageData[i][j] > 0.5) {
                            let directions = [];

                            // Check 8 directions
                            for (let di = -1; di <= 1; di++) {
                                for (let dj = -1; dj <= 1; dj++) {
                                    if (di !== 0 || dj !== 0) {
                                        if (imageData[i + di][j + dj] > 0.5) {
                                            directions.push([di, dj]);
                                        }
                                    }
                                }
                            }

                            // If we have 2 neighbors, check if they form a curve
                            if (directions.length === 2) {
                                const [d1, d2] = directions;
                                const dot = d1[0] * d2[0] + d1[1] * d2[1];
                                if (dot === 0) { // Perpendicular = curve
                                    curveScore += 0.1;
                                }
                            }
                            samples++;
                        }
                    }
                }

                return samples > 0 ? curveScore / samples : 0;
            }

            intelligentClassification(features, denseOutputs) {
                // Get the actual image data for direct analysis
                const imageData = this.layerOutputs.input;

                // Simple and effective pattern matching
                const scores = this.simplePatternMatching(imageData, features);

                // Apply training progress
                const trainingBonus = this.trainingProgress * this.tuningParams.trainingIntensity;

                // Final scoring with parameters
                const finalScores = new Array(10).fill(0);
                for (let i = 0; i < 10; i++) {
                    finalScores[i] = scores[i] * this.tuningParams.patternSensitivity * (1 + trainingBonus);

                    // Add small amount of noise
                    const noise = (Math.random() - 0.5) * (1 - this.tuningParams.noiseReduction) * 0.1;
                    finalScores[i] = Math.max(0.01, finalScores[i] + noise);
                }

                // Simple normalization
                const total = finalScores.reduce((a, b) => a + b, 0);
                return finalScores.map(s => s / total);
            }

            simplePatternMatching(imageData, features) {
                const scores = new Array(10).fill(0.1); // Base score for all

                // Direct pattern analysis based on actual image content
                const analysis = this.analyzeImagePattern(imageData);

                // Score each digit based on pattern analysis
                scores[0] = this.scoreDigit0Simple(analysis); // Circle
                scores[1] = this.scoreDigit1Simple(analysis); // Vertical line
                scores[2] = this.scoreDigit2Simple(analysis); // Horizontal segments
                scores[3] = this.scoreDigit3Simple(analysis); // Right curves
                scores[4] = this.scoreDigit4Simple(analysis); // Intersection
                scores[5] = this.scoreDigit5Simple(analysis); // S-shape
                scores[6] = this.scoreDigit6Simple(analysis); // Bottom loop
                scores[7] = this.scoreDigit7Simple(analysis); // Diagonal
                scores[8] = this.scoreDigit8Simple(analysis); // Double loop
                scores[9] = this.scoreDigit9Simple(analysis); // Top loop

                return scores;
            }

            analyzeImagePattern(imageData) {
                const analysis = {
                    hasLoop: false,
                    hasVerticalLine: false,
                    hasHorizontalLine: false,
                    hasDiagonal: false,
                    isSymmetric: false,
                    topHeavy: false,
                    bottomHeavy: false,
                    leftHeavy: false,
                    rightHeavy: false,
                    centerDense: false,
                    edgeCount: 0,
                    aspectRatio: 1.0
                };

                // Count pixels in different regions
                let topPixels = 0, bottomPixels = 0, leftPixels = 0, rightPixels = 0, centerPixels = 0;
                let totalPixels = 0;

                for (let i = 0; i < 28; i++) {
                    for (let j = 0; j < 28; j++) {
                        const pixel = imageData[i] ? imageData[i][j] || 0 : 0;
                        if (pixel > 0.3) {
                            totalPixels++;

                            if (i < 9) topPixels++;
                            if (i > 18) bottomPixels++;
                            if (j < 9) leftPixels++;
                            if (j > 18) rightPixels++;
                            if (i >= 9 && i <= 18 && j >= 9 && j <= 18) centerPixels++;
                        }
                    }
                }

                if (totalPixels > 0) {
                    analysis.topHeavy = topPixels / totalPixels > 0.4;
                    analysis.bottomHeavy = bottomPixels / totalPixels > 0.4;
                    analysis.leftHeavy = leftPixels / totalPixels > 0.4;
                    analysis.rightHeavy = rightPixels / totalPixels > 0.4;
                    analysis.centerDense = centerPixels / totalPixels > 0.3;
                }

                // Check for lines and patterns
                analysis.hasVerticalLine = this.detectVerticalLine(imageData);
                analysis.hasHorizontalLine = this.detectHorizontalLine(imageData);
                analysis.hasDiagonal = this.detectDiagonal(imageData);
                analysis.hasLoop = this.detectLoop(imageData);
                analysis.isSymmetric = this.detectSymmetry(imageData);

                return analysis;
            }

            detectVerticalLine(imageData) {
                let maxVerticalStrength = 0;
                let verticalColumns = 0;

                // Check multiple columns for vertical lines
                for (let j = 8; j < 20; j++) {
                    let strength = 0;
                    let consecutivePixels = 0;
                    let maxConsecutive = 0;

                    for (let i = 3; i < 25; i++) {
                        if (imageData[i] && imageData[i][j] > 0.3) {
                            strength++;
                            consecutivePixels++;
                            maxConsecutive = Math.max(maxConsecutive, consecutivePixels);
                        } else {
                            consecutivePixels = 0;
                        }
                    }

                    if (strength > 10 && maxConsecutive > 8) {
                        verticalColumns++;
                        maxVerticalStrength = Math.max(maxVerticalStrength, strength);
                    }
                }

                return maxVerticalStrength > 12 && verticalColumns >= 1;
            }

            detectHorizontalLine(imageData) {
                let horizontalLines = 0;
                let totalHorizontalStrength = 0;

                // Check multiple rows for horizontal lines
                for (let i = 3; i < 25; i += 3) {
                    let strength = 0;
                    let consecutivePixels = 0;
                    let maxConsecutive = 0;

                    for (let j = 6; j < 22; j++) {
                        if (imageData[i] && imageData[i][j] > 0.3) {
                            strength++;
                            consecutivePixels++;
                            maxConsecutive = Math.max(maxConsecutive, consecutivePixels);
                        } else {
                            consecutivePixels = 0;
                        }
                    }

                    if (strength > 6 && maxConsecutive > 4) {
                        horizontalLines++;
                        totalHorizontalStrength += strength;
                    }
                }

                return horizontalLines >= 2 || totalHorizontalStrength > 20;
            }

            detectDiagonal(imageData) {
                let diagonalStrength = 0;
                for (let i = 5; i < 23; i++) {
                    const j = Math.round(20 - (i - 5) * 0.8); // Diagonal from top-right to bottom-left
                    if (j >= 0 && j < 28 && imageData[i] && imageData[i][j] > 0.3) {
                        diagonalStrength++;
                    }
                }
                return diagonalStrength > 8;
            }

            detectLoop(imageData) {
                // Advanced loop detection - check for enclosed areas
                let loopScore = 0;
                let enclosedAreas = 0;

                // Check for enclosed areas in different regions
                for (let centerI = 10; centerI < 18; centerI += 2) {
                    for (let centerJ = 10; centerJ < 18; centerJ += 2) {
                        if (imageData[centerI] && imageData[centerI][centerJ] < 0.2) { // Empty center
                            let surrounding = 0;
                            let totalSurrounding = 0;

                            // Check in a circle around the center point
                            for (let radius = 2; radius <= 4; radius++) {
                                for (let angle = 0; angle < 2 * Math.PI; angle += Math.PI / 4) {
                                    const ni = Math.round(centerI + radius * Math.sin(angle));
                                    const nj = Math.round(centerJ + radius * Math.cos(angle));

                                    if (ni >= 0 && ni < 28 && nj >= 0 && nj < 28) {
                                        totalSurrounding++;
                                        if (imageData[ni] && imageData[ni][nj] > 0.4) {
                                            surrounding++;
                                        }
                                    }
                                }
                            }

                            if (totalSurrounding > 0 && surrounding / totalSurrounding > 0.6) {
                                loopScore++;
                                enclosedAreas++;
                            }
                        }
                    }
                }

                // Also check for circular patterns
                const centerX = 14, centerY = 14;
                let circularPixels = 0;
                let totalCircularSamples = 0;

                for (let radius = 6; radius <= 10; radius++) {
                    for (let angle = 0; angle < 2 * Math.PI; angle += Math.PI / 8) {
                        const i = Math.round(centerY + radius * Math.sin(angle));
                        const j = Math.round(centerX + radius * Math.cos(angle));

                        if (i >= 0 && i < 28 && j >= 0 && j < 28) {
                            totalCircularSamples++;
                            if (imageData[i] && imageData[i][j] > 0.3) {
                                circularPixels++;
                            }
                        }
                    }
                }

                const circularRatio = totalCircularSamples > 0 ? circularPixels / totalCircularSamples : 0;

                return loopScore > 3 || enclosedAreas > 1 || circularRatio > 0.5;
            }

            detectSymmetry(imageData) {
                let symmetryScore = 0;
                let comparisons = 0;
                for (let i = 0; i < 28; i++) {
                    for (let j = 0; j < 14; j++) {
                        const left = imageData[i] ? imageData[i][j] || 0 : 0;
                        const right = imageData[i] ? imageData[i][27 - j] || 0 : 0;
                        symmetryScore += 1 - Math.abs(left - right);
                        comparisons++;
                    }
                }
                return comparisons > 0 ? (symmetryScore / comparisons) > 0.8 : false;
            }

            // Improved scoring functions for each digit
            scoreDigit0Simple(analysis) {
                let score = 0.1;

                // Strong indicators for 0
                if (analysis.hasLoop) score += 4.0;
                if (analysis.isSymmetric) score += 3.0;
                if (analysis.centerDense) score += 2.0;

                // Negative indicators (penalize if present)
                if (analysis.hasVerticalLine) score -= 1.5;
                if (analysis.hasHorizontalLine) score -= 1.0;
                if (analysis.hasDiagonal) score -= 2.0;

                // Balanced distribution
                if (!analysis.topHeavy && !analysis.bottomHeavy) score += 1.0;
                if (!analysis.leftHeavy && !analysis.rightHeavy) score += 1.0;

                return Math.max(0.1, score);
            }

            scoreDigit1Simple(analysis) {
                let score = 0.1;

                // Strong indicators for 1
                if (analysis.hasVerticalLine) score += 5.0;
                if (analysis.centerDense) score += 2.0;

                // Negative indicators
                if (analysis.hasHorizontalLine) score -= 2.0;
                if (analysis.hasLoop) score -= 3.0;
                if (analysis.hasDiagonal) score -= 2.0;
                if (analysis.isSymmetric) score -= 1.0;

                // Should be narrow (not left/right heavy)
                if (!analysis.leftHeavy && !analysis.rightHeavy) score += 1.5;

                return Math.max(0.1, score);
            }

            scoreDigit2Simple(analysis) {
                let score = 0.1;

                // Strong indicators for 2
                if (analysis.hasHorizontalLine) score += 4.0;
                if (analysis.topHeavy && analysis.bottomHeavy) score += 3.0;
                if (analysis.hasDiagonal) score += 2.0;

                // Negative indicators
                if (analysis.hasLoop) score -= 3.0;
                if (analysis.isSymmetric) score -= 1.5;
                if (analysis.hasVerticalLine) score -= 1.0;

                // Should span width
                if (analysis.leftHeavy && analysis.rightHeavy) score += 1.0;

                return Math.max(0.1, score);
            }

            scoreDigit3Simple(analysis) {
                let score = 0.1;

                // Strong indicators for 3
                if (analysis.hasHorizontalLine) score += 3.0;
                if (analysis.rightHeavy) score += 3.0;
                if (analysis.topHeavy && analysis.bottomHeavy) score += 2.0;

                // Negative indicators
                if (analysis.hasVerticalLine) score -= 2.0;
                if (analysis.hasLoop) score -= 1.0;
                if (analysis.leftHeavy) score -= 1.5;
                if (analysis.isSymmetric) score -= 1.0;

                return Math.max(0.1, score);
            }

            scoreDigit4Simple(analysis) {
                let score = 0.1;

                // Strong indicators for 4
                if (analysis.hasVerticalLine && analysis.hasHorizontalLine) score += 5.0;
                if (analysis.leftHeavy) score += 2.0;
                if (!analysis.bottomHeavy) score += 2.0; // Open at bottom

                // Negative indicators
                if (analysis.hasLoop) score -= 3.0;
                if (analysis.isSymmetric) score -= 1.5;
                if (analysis.hasDiagonal) score -= 1.0;

                // Should have intersection pattern
                if (analysis.centerDense) score += 1.0;

                return Math.max(0.1, score);
            }

            scoreDigit5Simple(analysis) {
                let score = 0.1;

                // Strong indicators for 5
                if (analysis.hasHorizontalLine) score += 4.0;
                if (analysis.topHeavy && analysis.bottomHeavy) score += 3.0;
                if (!analysis.isSymmetric) score += 2.0;

                // Negative indicators
                if (analysis.hasLoop) score -= 1.0;
                if (analysis.hasVerticalLine) score -= 1.5;
                if (analysis.hasDiagonal) score -= 1.0;

                // S-shape characteristics
                if (analysis.leftHeavy && analysis.rightHeavy) score += 1.0;

                return Math.max(0.1, score);
            }

            scoreDigit6Simple(analysis) {
                let score = 0.1;

                // Strong indicators for 6
                if (analysis.hasLoop) score += 4.0;
                if (analysis.bottomHeavy) score += 3.0;
                if (analysis.leftHeavy) score += 2.0;
                if (!analysis.isSymmetric) score += 2.0;

                // Negative indicators
                if (analysis.hasDiagonal) score -= 2.0;
                if (analysis.topHeavy && analysis.bottomHeavy) score -= 1.0; // Should be bottom heavy

                return Math.max(0.1, score);
            }

            scoreDigit7Simple(analysis) {
                let score = 0.1;

                // Strong indicators for 7
                if (analysis.hasDiagonal) score += 5.0;
                if (analysis.topHeavy) score += 3.0;
                if (analysis.rightHeavy) score += 2.0;
                if (!analysis.bottomHeavy) score += 2.0;

                // Negative indicators
                if (analysis.hasLoop) score -= 3.0;
                if (analysis.isSymmetric) score -= 2.0;
                if (analysis.hasVerticalLine) score -= 1.5;
                if (analysis.leftHeavy) score -= 1.0;

                return Math.max(0.1, score);
            }

            scoreDigit8Simple(analysis) {
                let score = 0.1;

                // Strong indicators for 8
                if (analysis.hasLoop) score += 4.0;
                if (analysis.isSymmetric) score += 4.0;
                if (analysis.topHeavy && analysis.bottomHeavy) score += 3.0;
                if (analysis.centerDense) score += 2.0;

                // Negative indicators
                if (analysis.hasDiagonal) score -= 2.0;
                if (analysis.hasVerticalLine) score -= 1.0;

                // Should be balanced
                if (!analysis.leftHeavy && !analysis.rightHeavy) score += 1.0;

                return Math.max(0.1, score);
            }

            scoreDigit9Simple(analysis) {
                let score = 0.1;

                // Strong indicators for 9
                if (analysis.hasLoop) score += 4.0;
                if (analysis.topHeavy) score += 3.0;
                if (analysis.rightHeavy) score += 3.0;
                if (!analysis.isSymmetric) score += 2.0;

                // Negative indicators
                if (analysis.bottomHeavy && !analysis.topHeavy) score -= 2.0; // Should be top heavy
                if (analysis.hasDiagonal) score -= 1.5;
                if (analysis.leftHeavy) score -= 1.0;

                // Should have vertical component (tail)
                if (analysis.hasVerticalLine) score += 1.0;

                return Math.max(0.1, score);
            }

            calculateBiasCorrection(templateScores) {
                const correction = new Array(10).fill(1.0);

                // Find the maximum score
                const maxScore = Math.max(...templateScores);
                const minScore = Math.min(...templateScores);
                const scoreRange = maxScore - minScore;

                // If one score dominates too much, reduce its influence
                if (scoreRange > 0.3) {
                    for (let i = 0; i < 10; i++) {
                        if (templateScores[i] === maxScore && maxScore > 0.6) {
                            // Reduce dominance of highest score
                            correction[i] = 0.7;
                        } else if (templateScores[i] < 0.1) {
                            // Boost very low scores slightly
                            correction[i] = 1.2;
                        }
                    }
                }

                // Special bias correction for commonly confused digits
                const confusionCorrection = this.getConfusionCorrection(templateScores);
                for (let i = 0; i < 10; i++) {
                    correction[i] *= confusionCorrection[i];
                }

                return correction;
            }

            getConfusionCorrection(scores) {
                const correction = new Array(10).fill(1.0);

                // If 4 and 9 are both high, apply discrimination
                if (scores[4] > 0.4 && scores[9] > 0.4) {
                    // Prefer the one with higher score, but not by too much
                    if (scores[4] > scores[9]) {
                        correction[4] = 1.1;
                        correction[9] = 0.8;
                    } else {
                        correction[9] = 1.1;
                        correction[4] = 0.8;
                    }
                }

                // If 6 and 8 are both high, apply discrimination
                if (scores[6] > 0.4 && scores[8] > 0.4) {
                    if (scores[6] > scores[8]) {
                        correction[6] = 1.1;
                        correction[8] = 0.8;
                    } else {
                        correction[8] = 1.1;
                        correction[6] = 0.8;
                    }
                }

                // If 0 and 8 are both high, apply discrimination
                if (scores[0] > 0.4 && scores[8] > 0.4) {
                    if (scores[0] > scores[8]) {
                        correction[0] = 1.1;
                        correction[8] = 0.8;
                    } else {
                        correction[8] = 1.1;
                        correction[0] = 0.8;
                    }
                }

                return correction;
            }

            applySoftmax(scores) {
                // Apply temperature scaling for better distribution
                const temperature = 2.0; // Higher temperature = more uniform distribution
                const scaledScores = scores.map(s => s / temperature);

                // Find max for numerical stability
                const maxScore = Math.max(...scaledScores);
                const expScores = scaledScores.map(s => Math.exp(s - maxScore));
                const sumExp = expScores.reduce((a, b) => a + b, 0);

                return expScores.map(s => s / sumExp);
            }

            templateMatching(imageData) {
                if (!imageData) return new Array(10).fill(0.1);

                const scores = new Array(10).fill(0);

                // Generate reference templates for each digit
                const templates = this.generateDigitTemplates();

                // Calculate correlation with each template
                for (let digit = 0; digit < 10; digit++) {
                    scores[digit] = this.calculateCorrelation(imageData, templates[digit]);
                }

                return scores;
            }

            generateDigitTemplates() {
                const templates = {};

                for (let digit = 0; digit < 10; digit++) {
                    const template = [];
                    for (let i = 0; i < 28; i++) {
                        template[i] = [];
                        for (let j = 0; j < 28; j++) {
                            template[i][j] = this.generateCleanDigitPixel(digit, i, j);
                        }
                    }
                    templates[digit] = template;
                }

                return templates;
            }

            generateCleanDigitPixel(digit, row, col) {
                const centerX = 14, centerY = 14;
                const distFromCenter = Math.sqrt((row - centerY) ** 2 + (col - centerX) ** 2);

                switch (digit) {
                    case 0: // Perfect oval/circle - UNIQUE: closed loop, symmetric
                        const ovalA = 8; // horizontal radius
                        const ovalB = 10; // vertical radius
                        const ovalDist = Math.sqrt(((col - centerX) / ovalA) ** 2 + ((row - centerY) / ovalB) ** 2);
                        return (ovalDist >= 0.7 && ovalDist <= 1.0) ? 1.0 : 0.0;

                    case 1: // Thin vertical line - UNIQUE: minimal width, maximum height
                        return (col >= 13 && col <= 15 && row >= 3 && row <= 25) ? 1.0 : 0.0;

                    case 2: // Z-shape - UNIQUE: three horizontal segments + diagonal
                        const is2 = (row >= 3 && row <= 5 && col >= 7 && col <= 21) || // top horizontal
                                   (row >= 23 && row <= 25 && col >= 7 && col <= 21) || // bottom horizontal
                                   (row >= 13 && row <= 15 && col >= 7 && col <= 21) || // middle horizontal
                                   (Math.abs(row - col + 8) <= 1 && row >= 6 && row <= 12 && col >= 14); // diagonal
                        return is2 ? 1.0 : 0.0;

                    case 3: // Right-open curves - UNIQUE: open on left, curves on right
                        const is3 = (row >= 3 && row <= 5 && col >= 7 && col <= 19) || // top
                                   (row >= 23 && row <= 25 && col >= 7 && col <= 19) || // bottom
                                   (row >= 13 && row <= 15 && col >= 7 && col <= 17) || // middle
                                   (col >= 19 && col <= 21 && ((row >= 6 && row <= 12) || (row >= 16 && row <= 22))); // right curves
                        return is3 ? 1.0 : 0.0;

                    case 4: // Open bottom triangle - UNIQUE: open at bottom, intersection
                        const is4 = (col >= 13 && col <= 15 && row >= 3 && row <= 25) || // main vertical
                                   (row >= 13 && row <= 15 && col >= 5 && col <= 19) || // horizontal crossbar
                                   (col >= 5 && col <= 7 && row >= 3 && row <= 15); // left vertical (stops at crossbar)
                        return is4 ? 1.0 : 0.0;

                    case 5: // S-curve - UNIQUE: left-top, right-bottom pattern
                        const is5 = (row >= 3 && row <= 5 && col >= 7 && col <= 21) || // top
                                   (row >= 23 && row <= 25 && col >= 7 && col <= 21) || // bottom
                                   (row >= 13 && row <= 15 && col >= 7 && col <= 19) || // middle
                                   (col >= 5 && col <= 7 && row >= 3 && row <= 15) || // left top
                                   (col >= 19 && col <= 21 && row >= 15 && row <= 25); // right bottom
                        return is5 ? 1.0 : 0.0;

                    case 6: // Bottom loop - UNIQUE: closed bottom, open top-right
                        const is6Bottom = Math.sqrt((row - 19) ** 2 + (col - 14) ** 2);
                        const is6 = (is6Bottom >= 4 && is6Bottom <= 7) || // bottom loop
                                   (col >= 5 && col <= 7 && row >= 3 && row <= 19) || // left vertical
                                   (row >= 3 && row <= 5 && col >= 7 && col <= 19) || // top horizontal
                                   (row >= 13 && row <= 15 && col >= 7 && col <= 19); // middle horizontal
                        return is6 ? 1.0 : 0.0;

                    case 7: // L-inverted - UNIQUE: top horizontal + right diagonal
                        const is7 = (row >= 3 && row <= 5 && col >= 7 && col <= 21) || // top horizontal
                                   (Math.abs((row - 5) * 1.1 - (col - 21)) <= 1.5 && row >= 6 && row <= 25 && col >= 15); // diagonal
                        return is7 ? 1.0 : 0.0;

                    case 8: // Double loop - UNIQUE: two separate circles connected
                        const top8 = Math.sqrt((row - 8) ** 2 + (col - 14) ** 2);
                        const bottom8 = Math.sqrt((row - 20) ** 2 + (col - 14) ** 2);
                        const is8 = (top8 >= 3.5 && top8 <= 6.5) || // top circle
                                   (bottom8 >= 3.5 && bottom8 <= 6.5) || // bottom circle
                                   (row >= 12 && row <= 16 && col >= 8 && col <= 20); // middle connection
                        return is8 ? 1.0 : 0.0;

                    case 9: // Top loop with tail - UNIQUE: closed top, open bottom-right
                        const is9Top = Math.sqrt((row - 9) ** 2 + (col - 14) ** 2);
                        const is9 = (is9Top >= 4 && is9Top <= 7) || // top loop
                                   (col >= 19 && col <= 21 && row >= 9 && row <= 25) || // right tail
                                   (row >= 13 && row <= 15 && col >= 7 && col <= 21); // middle horizontal
                        return is9 ? 1.0 : 0.0;

                    default:
                        return 0.0;
                }
            }

            calculateCorrelation(imageData, template) {
                // Calculate multiple correlation metrics for better discrimination
                const metrics = this.calculateMultipleMetrics(imageData, template);

                // Combine metrics with weights
                const combinedScore =
                    metrics.normalizedCorrelation * 0.4 +
                    metrics.structuralSimilarity * 0.3 +
                    metrics.shapeMatch * 0.2 +
                    metrics.densityMatch * 0.1;

                return Math.max(0, combinedScore);
            }

            calculateMultipleMetrics(imageData, template) {
                let correlation = 0;
                let imageSum = 0;
                let templateSum = 0;
                let imageMean = 0;
                let templateMean = 0;
                let count = 0;

                // Calculate means first
                for (let i = 0; i < 28; i++) {
                    for (let j = 0; j < 28; j++) {
                        const imgPixel = imageData[i] ? imageData[i][j] || 0 : 0;
                        const tempPixel = template[i][j];
                        imageMean += imgPixel;
                        templateMean += tempPixel;
                        count++;
                    }
                }
                imageMean /= count;
                templateMean /= count;

                // Calculate correlation and variances
                let imageVar = 0;
                let templateVar = 0;
                let covariance = 0;
                let structuralMatch = 0;
                let shapeMatch = 0;

                for (let i = 0; i < 28; i++) {
                    for (let j = 0; j < 28; j++) {
                        const imgPixel = imageData[i] ? imageData[i][j] || 0 : 0;
                        const tempPixel = template[i][j];

                        const imgDiff = imgPixel - imageMean;
                        const tempDiff = tempPixel - templateMean;

                        correlation += imgPixel * tempPixel;
                        imageVar += imgDiff * imgDiff;
                        templateVar += tempDiff * tempDiff;
                        covariance += imgDiff * tempDiff;

                        // Structural similarity (both high or both low)
                        if ((imgPixel > 0.5 && tempPixel > 0.5) || (imgPixel <= 0.5 && tempPixel <= 0.5)) {
                            structuralMatch += 1;
                        }

                        // Shape matching (edge alignment)
                        if (imgPixel > 0.3 && tempPixel > 0.3) {
                            shapeMatch += Math.min(imgPixel, tempPixel);
                        }
                    }
                }

                // Normalized correlation
                const denominator = Math.sqrt(imageVar * templateVar);
                const normalizedCorrelation = denominator > 0 ? covariance / denominator : 0;

                // Structural similarity index
                const structuralSimilarity = structuralMatch / count;

                // Shape match score
                const shapeMatchScore = shapeMatch / Math.max(1, this.countActivePixels(template));

                // Density match
                const imageDensity = this.countActivePixels(imageData) / count;
                const templateDensity = this.countActivePixels(template) / count;
                const densityMatch = 1 - Math.abs(imageDensity - templateDensity);

                return {
                    normalizedCorrelation: Math.max(0, normalizedCorrelation),
                    structuralSimilarity: structuralSimilarity,
                    shapeMatch: shapeMatchScore,
                    densityMatch: densityMatch
                };
            }

            countActivePixels(imageData) {
                let count = 0;
                for (let i = 0; i < 28; i++) {
                    for (let j = 0; j < 28; j++) {
                        if ((imageData[i] ? imageData[i][j] || 0 : 0) > 0.3) {
                            count++;
                        }
                    }
                }
                return count;
            }

            scoreDigit0(features) {
                let score = 0;

                // Strong circular pattern
                score += features.circularPattern * 3;

                // Balanced left/right density
                const leftRightBalance = 1 - Math.abs(features.leftDensity - features.rightDensity);
                score += leftRightBalance * 2;

                // High vertical symmetry
                score += features.symmetryVertical * 2;

                // Low straight pattern (should be curved)
                score += (1 - features.straightPattern) * 1;

                // Moderate compactness (not too dense)
                score += (features.compactness > 0.3 && features.compactness < 0.7) ? 1 : 0;

                // Penalize too many horizontal lines
                score -= features.horizontalLines * 0.5;

                return score;
            }

            scoreDigit1(features) {
                let score = 0;

                // Strong vertical lines
                score += features.verticalLines * 4;

                // High straight pattern
                score += features.straightPattern * 3;

                // Low horizontal lines
                score += (1 - features.horizontalLines) * 2;

                // Narrow aspect ratio (tall and thin)
                score += (features.aspectRatio < 0.6) ? 2 : 0;

                // Center density should be high
                score += features.centerDensity * 2;

                // Low circular pattern
                score += (1 - features.circularPattern) * 1;

                // Few endpoints (should be mostly one line)
                score += (features.endpoints < 0.5) ? 1 : 0;

                return score;
            }

            scoreDigit2(features) {
                let score = 0;

                // Strong horizontal lines
                score += features.horizontalLines * 3;

                // High top and bottom density
                score += (features.topDensity + features.bottomDensity) * 1.5;

                // Moderate middle density
                score += (features.middleDensity > 0.3 && features.middleDensity < 0.8) ? 1 : 0;

                // Low circular pattern
                score += (1 - features.circularPattern) * 2;

                // Some diagonal elements
                score += features.diagonalLines * 0.5;

                // Multiple endpoints
                score += (features.endpoints > 0.3) ? 1 : 0;

                // Low vertical symmetry
                score += (1 - features.symmetryVertical) * 0.5;

                return score;
            }

            scoreDigit3(features) {
                let score = 0;

                // Curved pattern
                score += features.curvedPattern * 3;

                // Strong horizontal lines
                score += features.horizontalLines * 2;

                // Right-heavy density
                score += (features.rightDensity > features.leftDensity) ? 2 : 0;

                // High top and bottom density
                score += (features.topDensity + features.bottomDensity) * 1.5;

                // Low vertical symmetry
                score += (1 - features.symmetryVertical) * 1;

                // Some loops but not too many
                score += (features.loops > 0.1 && features.loops < 0.5) ? 1 : 0;

                return score;
            }

            scoreDigit4(features) {
                let score = 0;

                // Strong vertical and horizontal lines
                score += (features.verticalLines + features.horizontalLines) * 2;

                // High intersections
                score += features.intersections * 3;

                // Left and center density
                score += (features.leftDensity + features.centerDensity) * 1.5;

                // Low bottom density (open bottom)
                score += (1 - features.bottomDensity) * 1;

                // Low circular pattern
                score += (1 - features.circularPattern) * 2;

                // High straight pattern
                score += features.straightPattern * 2;

                return score;
            }

            scoreDigit5(features) {
                let score = 0;

                // Strong horizontal lines
                score += features.horizontalLines * 3;

                // High top, middle, and bottom density
                score += (features.topDensity + features.middleDensity + features.bottomDensity) * 1;

                // Left-heavy in top, right-heavy in bottom
                const topLeftHeavy = features.topDensity > 0.3 && features.leftDensity > features.rightDensity;
                const bottomRightHeavy = features.bottomDensity > 0.3 && features.rightDensity > 0.3;
                score += (topLeftHeavy && bottomRightHeavy) ? 2 : 0;

                // Some curved elements
                score += features.curvedPattern * 1;

                // Low vertical symmetry
                score += (1 - features.symmetryVertical) * 1;

                return score;
            }

            scoreDigit6(features) {
                let score = 0;

                // Strong circular pattern (but not complete circle)
                score += features.circularPattern * 2;

                // High bottom density, moderate top
                score += features.bottomDensity * 2;
                score += (features.topDensity > 0.2 && features.topDensity < 0.6) ? 1 : 0;

                // Some loops
                score += features.loops * 2;

                // Curved pattern
                score += features.curvedPattern * 2;

                // Left-heavy density
                score += (features.leftDensity > features.rightDensity) ? 1 : 0;

                // Low horizontal symmetry
                score += (1 - features.symmetryHorizontal) * 1;

                return score;
            }

            scoreDigit7(features) {
                let score = 0;

                // Strong diagonal lines
                score += features.diagonalLines * 4;

                // High top density
                score += features.topDensity * 3;

                // Strong horizontal lines at top
                score += features.horizontalLines * 2;

                // Low bottom density
                score += (1 - features.bottomDensity) * 1;

                // Right-heavy density
                score += (features.rightDensity > features.leftDensity) ? 2 : 0;

                // Low circular pattern
                score += (1 - features.circularPattern) * 2;

                // High straight pattern
                score += features.straightPattern * 1;

                return score;
            }

            scoreDigit8(features) {
                let score = 0;

                // Strong circular pattern
                score += features.circularPattern * 3;

                // High loops
                score += features.loops * 4;

                // High vertical symmetry
                score += features.symmetryVertical * 3;

                // High horizontal symmetry
                score += features.symmetryHorizontal * 2;

                // Balanced density in all regions
                const balanced = 1 - Math.abs(features.topDensity - features.bottomDensity);
                score += balanced * 2;

                // High compactness
                score += features.compactness * 2;

                // Some intersections
                score += features.intersections * 1;

                return score;
            }

            scoreDigit9(features) {
                let score = 0;

                // Strong circular pattern
                score += features.circularPattern * 2;

                // High top density
                score += features.topDensity * 2;

                // Some loops
                score += features.loops * 2;

                // Curved pattern
                score += features.curvedPattern * 2;

                // Right-heavy density
                score += (features.rightDensity > features.leftDensity) ? 2 : 0;

                // High vertical lines (for the tail)
                score += features.verticalLines * 1;

                // Low horizontal symmetry
                score += (1 - features.symmetryHorizontal) * 1;

                return score;
            }

            updateTuningParameters() {
                this.tuningParams.learningRate = parseFloat(document.getElementById('learningRate').value);
                this.tuningParams.patternSensitivity = parseFloat(document.getElementById('patternSensitivity').value);
                this.tuningParams.featureStrength = parseFloat(document.getElementById('featureStrength').value);
                this.tuningParams.noiseReduction = parseFloat(document.getElementById('noiseReduction').value);
                this.tuningParams.trainingIntensity = parseFloat(document.getElementById('trainingIntensity').value);
            }

            simulateConvolution(input, features) {
                const outputs = [];
                this.calculations.convolution = [];

                for (let f = 0; f < this.config.convFilters; f++) {
                    // Simulate convolution for each filter
                    const featureMap = [];
                    const filterCalc = {
                        filterIndex: f,
                        inputSize: [28, 28],
                        filterSize: this.config.filterSize,
                        outputSize: [26, 26], // 28-3+1
                        sampleCalculations: []
                    };

                    // Sample calculation for position (5,5)
                    let convSum = 0;
                    const sampleInputs = [];
                    const sampleWeights = [];

                    for (let i = 0; i < this.config.filterSize; i++) {
                        for (let j = 0; j < this.config.filterSize; j++) {
                            const inputVal = input[5 + i] ? input[5 + i][5 + j] || 0 : 0;
                            const weight = (Math.sin(f + i + j) + 1) / 2; // Simulated weight
                            sampleInputs.push(inputVal);
                            sampleWeights.push(weight);
                            convSum += inputVal * weight;
                        }
                    }

                    const activation = Math.max(0, convSum); // ReLU

                    filterCalc.sampleCalculations.push({
                        position: [5, 5],
                        inputs: sampleInputs,
                        weights: sampleWeights,
                        convSum: convSum,
                        activation: activation
                    });

                    // Generate feature map values
                    for (let i = 0; i < 26; i++) {
                        featureMap[i] = [];
                        for (let j = 0; j < 26; j++) {
                            featureMap[i][j] = Math.random() * 0.5 + activation * 0.3;
                        }
                    }

                    outputs.push(featureMap);
                    this.calculations.convolution.push(filterCalc);
                }

                return outputs;
            }

            simulatePooling(convOutputs) {
                const outputs = [];
                this.calculations.pooling = [];

                for (let f = 0; f < convOutputs.length; f++) {
                    const pooled = [];
                    const poolCalc = {
                        filterIndex: f,
                        inputSize: [26, 26],
                        poolSize: this.config.poolSize,
                        outputSize: [13, 13], // 26/2
                        sampleCalculations: []
                    };

                    // Sample calculation for position (2,2) -> pool region (4,4) to (5,5)
                    const poolRegion = [];
                    for (let i = 0; i < this.config.poolSize; i++) {
                        for (let j = 0; j < this.config.poolSize; j++) {
                            const val = convOutputs[f][4 + i] ? convOutputs[f][4 + i][4 + j] || 0 : 0;
                            poolRegion.push(val);
                        }
                    }
                    const maxVal = Math.max(...poolRegion);

                    poolCalc.sampleCalculations.push({
                        outputPosition: [2, 2],
                        inputRegion: [[4, 4], [5, 5]],
                        values: poolRegion,
                        maxValue: maxVal
                    });

                    // Generate pooled values
                    for (let i = 0; i < 13; i++) {
                        pooled[i] = [];
                        for (let j = 0; j < 13; j++) {
                            pooled[i][j] = Math.random() * 0.4 + maxVal * 0.3;
                        }
                    }

                    outputs.push(pooled);
                    this.calculations.pooling.push(poolCalc);
                }

                return outputs;
            }

            simulateFlatten(poolOutputs) {
                const flattened = [];
                for (let f = 0; f < poolOutputs.length; f++) {
                    for (let i = 0; i < 13; i++) {
                        for (let j = 0; j < 13; j++) {
                            flattened.push(poolOutputs[f][i][j]);
                        }
                    }
                }
                return flattened; // 8 * 13 * 13 = 1352 values
            }

            simulateDense(flattened) {
                const outputs = [];
                this.calculations.dense = [];

                for (let i = 0; i < this.config.denseUnits; i++) {
                    let sum = 0;
                    const sampleWeights = [];
                    const sampleInputs = [];

                    // Sample first 9 connections for tooltip
                    for (let j = 0; j < Math.min(9, flattened.length); j++) {
                        const weight = (Math.sin(i + j) + 1) / 2; // Simulated weight
                        sampleWeights.push(weight);
                        sampleInputs.push(flattened[j]);
                        sum += flattened[j] * weight;
                    }

                    const bias = (Math.cos(i) + 1) / 4; // Simulated bias
                    const preActivation = sum + bias;
                    const activation = Math.max(0, preActivation); // ReLU

                    outputs.push(activation);

                    if (i < 8) { // Store calculations for first 8 neurons for tooltips
                        this.calculations.dense.push({
                            neuronIndex: i,
                            sampleInputs: sampleInputs,
                            sampleWeights: sampleWeights,
                            weightedSum: sum,
                            bias: bias,
                            preActivation: preActivation,
                            activation: activation,
                            totalConnections: flattened.length
                        });
                    }
                }

                return outputs;
            }

            simulateOutput(denseOutputs) {
                const logits = [];
                this.calculations.softmax = [];

                // Generate logits
                for (let i = 0; i < this.config.outputClasses; i++) {
                    let sum = 0;
                    const sampleWeights = [];
                    const sampleInputs = [];

                    // Sample connections for tooltip
                    for (let j = 0; j < Math.min(8, denseOutputs.length); j++) {
                        const weight = (Math.sin(i + j + 10) + 1) / 2;
                        sampleWeights.push(weight);
                        sampleInputs.push(denseOutputs[j]);
                        sum += denseOutputs[j] * weight;
                    }

                    const bias = (Math.cos(i + 5) + 1) / 4;
                    const logit = sum + bias;
                    logits.push(logit);

                    this.calculations.softmax.push({
                        classIndex: i,
                        sampleInputs: sampleInputs,
                        sampleWeights: sampleWeights,
                        weightedSum: sum,
                        bias: bias,
                        logit: logit
                    });
                }

                // Apply softmax
                const maxLogit = Math.max(...logits);
                const expLogits = logits.map(x => Math.exp(x - maxLogit));
                const sumExp = expLogits.reduce((a, b) => a + b, 0);
                const probabilities = expLogits.map(x => x / sumExp);

                // Update softmax calculations with final probabilities
                for (let i = 0; i < this.calculations.softmax.length; i++) {
                    this.calculations.softmax[i].expLogit = expLogits[i];
                    this.calculations.softmax[i].probability = probabilities[i];
                    this.calculations.softmax[i].sumExp = sumExp;
                }

                return probabilities;
            }
            
            simulateTraining() {
                this.epoch++;

                // Update tuning parameters from UI
                this.updateTuningParameters();

                // Training progress affects learning
                const learningRate = this.tuningParams.learningRate;
                const trainingIntensity = this.tuningParams.trainingIntensity;

                // Progress calculation with learning rate effect
                const progressIncrement = learningRate * trainingIntensity * 0.02;
                this.trainingProgress = Math.min(1, this.trainingProgress + progressIncrement);

                // Loss calculation (decreases with training and better parameters)
                const baseLoss = 2.303;
                const learningEffect = this.trainingProgress * (1 + trainingIntensity);
                const parameterEffect = (this.tuningParams.patternSensitivity + this.tuningParams.featureStrength) / 4;
                const noiseEffect = this.tuningParams.noiseReduction * 0.5;

                const loss = baseLoss * Math.exp(-learningEffect * 2) * (1 - parameterEffect) * (1 - noiseEffect) +
                           0.05 * Math.random();

                // Accuracy calculation (improves with training and tuning)
                const baseAccuracy = 10;
                const maxAccuracy = 95;
                const trainingBonus = this.trainingProgress * 60 * trainingIntensity;
                const tuningBonus = (this.tuningParams.patternSensitivity +
                                   this.tuningParams.featureStrength +
                                   this.tuningParams.noiseReduction) * 8;
                const learningBonus = Math.log(1 + learningRate * 100) * 5;

                const accuracy = baseAccuracy + trainingBonus + tuningBonus + learningBonus +
                               (Math.random() - 0.5) * 3;

                // Validation accuracy (slightly different from training)
                const valAccuracy = accuracy * (0.9 + Math.random() * 0.15);

                this.lossHistory.push(loss);

                return {
                    epoch: this.epoch,
                    loss: Math.max(0.01, loss),
                    accuracy: Math.min(maxAccuracy, Math.max(baseAccuracy, accuracy)),
                    valAccuracy: Math.min(maxAccuracy, Math.max(baseAccuracy, valAccuracy))
                };
            }
        }
        
        // CNN App
        class CNNApp {
            constructor() {
                this.network = new SimpleCNN();
                this.isDrawing = false;
                this.trainingInterval = null;

                // Animation state
                this.animationState = {
                    isAnimating: false,
                    currentLayer: 0,
                    animationProgress: 0,
                    dataFlow: [],
                    layerActivations: [],
                    lastUpdateTime: 0
                };

                // Layer positions for animation
                this.layerPositions = [
                    { x: 100, y: 150, width: 80, height: 100, type: 'input' },
                    { x: 250, y: 120, width: 100, height: 160, type: 'conv' },
                    { x: 420, y: 150, width: 80, height: 100, type: 'pool' },
                    { x: 570, y: 170, width: 60, height: 60, type: 'dense' },
                    { x: 700, y: 175, width: 60, height: 50, type: 'output' }
                ];

                this.initializeApp();
            }
            
            initializeApp() {
                console.log('✅ Initializing CNN App...');
                
                this.setupCanvas();
                this.setupEventListeners();
                this.generateSampleDigits();
                this.loadSampleImage('digit0');
                this.drawNetwork();
                
                console.log('🎉 CNN App ready!');
            }
            
            setupCanvas() {
                this.canvas = document.getElementById('cnnCanvas');
                this.ctx = this.canvas.getContext('2d');

                this.drawingCanvas = document.getElementById('drawingCanvas');
                this.drawingCtx = this.drawingCanvas.getContext('2d');

                // Debug canvas for showing network input
                this.debugCanvas = document.getElementById('debugCanvas');
                this.debugCtx = this.debugCanvas.getContext('2d');

                // Tooltip elements
                this.tooltip = document.getElementById('cnnTooltip');
                this.tooltipTitle = this.tooltip.querySelector('.tooltip-title');
                this.tooltipContent = this.tooltip.querySelector('.tooltip-content');

                // Setup canvas mouse events for tooltips
                this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
                this.canvas.addEventListener('mouseleave', () => this.hideTooltip());

                this.clearDrawingCanvas();
                console.log('✅ Canvas setup complete');
            }
            
            setupEventListeners() {
                // Training controls
                document.getElementById('startCNNTraining').onclick = () => this.startTraining();
                document.getElementById('forwardPassCNN').onclick = () => this.demonstrateForwardPass();
                document.getElementById('resetCNN').onclick = () => this.resetNetwork();
                document.getElementById('pauseResumeCNN').onclick = () => this.togglePause();
                
                // Image controls
                document.getElementById('imageSelect').onchange = (e) => this.loadSampleImage(e.target.value);
                document.getElementById('classifyImage').onclick = () => this.classifyCurrentImage();
                document.getElementById('clearDrawing').onclick = () => this.clearDrawingCanvas();
                
                // Drawing events
                this.drawingCanvas.onmousedown = (e) => { this.isDrawing = true; this.draw(e); };
                this.drawingCanvas.onmousemove = (e) => { if (this.isDrawing) this.draw(e); };
                this.drawingCanvas.onmouseup = () => { this.isDrawing = false; };
                this.drawingCanvas.onmouseout = () => { this.isDrawing = false; };

                // Fine-tuning parameter controls
                this.setupTuningControls();

                console.log('✅ Event listeners setup complete');
            }

            setupTuningControls() {
                const controls = [
                    { id: 'learningRate', display: 'lrValue' },
                    { id: 'patternSensitivity', display: 'patternValue' },
                    { id: 'featureStrength', display: 'featureValue' },
                    { id: 'noiseReduction', display: 'noiseValue' },
                    { id: 'trainingIntensity', display: 'intensityValue' }
                ];

                controls.forEach(control => {
                    const slider = document.getElementById(control.id);
                    const display = document.getElementById(control.display);

                    if (slider && display) {
                        slider.oninput = () => {
                            display.textContent = slider.value;
                            this.network.updateTuningParameters();
                            // Re-classify current image with new parameters
                            this.classifyCurrentImage();
                        };
                    }
                });

                console.log('✅ Tuning controls setup complete');
            }
            
            generateSampleDigits() {
                this.sampleDigits = {};
                
                for (let digit = 0; digit < 10; digit++) {
                    const pattern = [];
                    for (let i = 0; i < 28; i++) {
                        pattern[i] = [];
                        for (let j = 0; j < 28; j++) {
                            pattern[i][j] = this.generateDigitPixel(digit, i, j);
                        }
                    }
                    this.sampleDigits[`digit${digit}`] = pattern;
                }
                
                console.log('✅ Sample digits generated');
            }
            
            generateDigitPixel(digit, row, col) {
                const centerX = 14, centerY = 14;
                const distFromCenter = Math.sqrt((row - centerY) ** 2 + (col - centerX) ** 2);
                const noise = Math.random() * 0.05; // Reduced noise

                let intensity = 0;

                switch (digit) {
                    case 0: // Circle - clear ring shape
                        if (distFromCenter > 5 && distFromCenter < 11) {
                            intensity = 0.9 - Math.abs(distFromCenter - 8) * 0.1;
                        }
                        break;

                    case 1: // Vertical line - straight and narrow
                        if (col >= 13 && col <= 15 && row >= 3 && row <= 25) {
                            intensity = 0.95;
                        }
                        break;

                    case 2: // Z-shape with horizontal lines
                        if ((row >= 3 && row <= 6 && col >= 8 && col <= 20) || // top
                            (row >= 24 && row <= 27 && col >= 8 && col <= 20) || // bottom
                            (row >= 13 && row <= 15 && col >= 8 && col <= 20) || // middle
                            (Math.abs(row - col + 5) <= 1 && row >= 6 && row <= 13)) { // diagonal
                            intensity = 0.9;
                        }
                        break;

                    case 3: // Two curves on right
                        if ((row >= 3 && row <= 6 && col >= 8 && col <= 18) || // top
                            (row >= 24 && row <= 27 && col >= 8 && col <= 18) || // bottom
                            (row >= 13 && row <= 15 && col >= 8 && col <= 16) || // middle
                            (col >= 18 && col <= 20 && ((row >= 7 && row <= 12) || (row >= 16 && row <= 23)))) { // right curves
                            intensity = 0.9;
                        }
                        break;

                    case 4: // T-shape inverted
                        if ((col >= 13 && col <= 15 && row >= 3 && row <= 25) || // main vertical
                            (row >= 13 && row <= 15 && col >= 6 && col <= 18) || // horizontal
                            (col >= 6 && col <= 8 && row >= 3 && row <= 15)) { // left vertical
                            intensity = 0.9;
                        }
                        break;

                    case 5: // S-shape
                        if ((row >= 3 && row <= 6 && col >= 8 && col <= 20) || // top
                            (row >= 24 && row <= 27 && col >= 8 && col <= 20) || // bottom
                            (row >= 13 && row <= 15 && col >= 8 && col <= 18) || // middle
                            (col >= 6 && col <= 8 && row >= 3 && row <= 15) || // left top
                            (col >= 18 && col <= 20 && row >= 15 && row <= 27)) { // right bottom
                            intensity = 0.9;
                        }
                        break;

                    case 6: // Circle with gap at top right
                        if ((distFromCenter > 5 && distFromCenter < 11 && !(col > 14 && row < 10)) ||
                            (row >= 13 && row <= 15 && col >= 8 && col <= 16)) { // horizontal line
                            intensity = 0.9;
                        }
                        break;

                    case 7: // L-shape inverted
                        if ((row >= 3 && row <= 6 && col >= 8 && col <= 20) || // top horizontal
                            (col >= 18 && col <= 20 && row >= 6 && row <= 25)) { // right vertical
                            intensity = 0.9;
                        }
                        break;

                    case 8: // Two circles stacked
                        const topCircle = Math.sqrt((row - 9) ** 2 + (col - 14) ** 2);
                        const bottomCircle = Math.sqrt((row - 19) ** 2 + (col - 14) ** 2);
                        if ((topCircle > 3 && topCircle < 7) ||
                            (bottomCircle > 3 && bottomCircle < 7) ||
                            (row >= 13 && row <= 15 && col >= 10 && col <= 18)) { // middle connection
                            intensity = 0.9;
                        }
                        break;

                    case 9: // Circle with tail
                        if ((distFromCenter > 5 && distFromCenter < 11 && row < 16) || // top circle
                            (col >= 18 && col <= 20 && row >= 10 && row <= 25)) { // right tail
                            intensity = 0.9;
                        }
                        break;
                }

                return Math.min(1, intensity + noise);
            }

            loadSampleImage(imageKey) {
                if (this.sampleDigits[imageKey]) {
                    this.drawImageOnCanvas(this.sampleDigits[imageKey]);
                    console.log(`📷 Loaded sample image: ${imageKey}`);
                }
            }

            drawImageOnCanvas(imageData) {
                const canvas = this.drawingCanvas;
                const ctx = this.drawingCtx;

                ctx.fillStyle = '#fff';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                const cellWidth = canvas.width / 28;
                const cellHeight = canvas.height / 28;

                for (let i = 0; i < 28; i++) {
                    for (let j = 0; j < 28; j++) {
                        const intensity = imageData[i][j];
                        const gray = Math.floor((1 - intensity) * 255);
                        ctx.fillStyle = `rgb(${gray}, ${gray}, ${gray})`;
                        ctx.fillRect(j * cellWidth, i * cellHeight, cellWidth, cellHeight);
                    }
                }

                ctx.strokeStyle = '#ddd';
                ctx.lineWidth = 2;
                ctx.strokeRect(0, 0, canvas.width, canvas.height);

                this.classifyCurrentImage();
            }

            draw(e) {
                const rect = this.drawingCanvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                this.drawingCtx.fillStyle = '#000';
                this.drawingCtx.beginPath();
                this.drawingCtx.arc(x, y, 8, 0, 2 * Math.PI);
                this.drawingCtx.fill();

                clearTimeout(this.drawingTimeout);
                this.drawingTimeout = setTimeout(() => {
                    this.classifyCurrentImage();
                }, 300);
            }

            clearDrawingCanvas() {
                this.drawingCtx.fillStyle = '#fff';
                this.drawingCtx.fillRect(0, 0, this.drawingCanvas.width, this.drawingCanvas.height);

                this.drawingCtx.strokeStyle = '#ddd';
                this.drawingCtx.lineWidth = 2;
                this.drawingCtx.strokeRect(0, 0, this.drawingCanvas.width, this.drawingCanvas.height);

                this.displayClassificationResult({
                    class: 0,
                    confidence: 0,
                    probabilities: new Array(10).fill(0.1)
                });

                console.log('🧹 Canvas cleared');
            }

            getImageFromCanvas() {
                const canvas = this.drawingCanvas;
                const ctx = this.drawingCtx;
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                const data = imageData.data;

                const result = [];
                const scaleX = canvas.width / 28;
                const scaleY = canvas.height / 28;

                // First pass: extract and downsample
                for (let i = 0; i < 28; i++) {
                    result[i] = [];
                    for (let j = 0; j < 28; j++) {
                        let sum = 0;
                        let count = 0;

                        // Sample multiple pixels for better downsampling
                        for (let dy = 0; dy < scaleY; dy++) {
                            for (let dx = 0; dx < scaleX; dx++) {
                                const x = Math.floor(j * scaleX + dx);
                                const y = Math.floor(i * scaleY + dy);

                                if (x < canvas.width && y < canvas.height) {
                                    const index = (y * canvas.width + x) * 4;
                                    const gray = (data[index] + data[index + 1] + data[index + 2]) / 3;
                                    sum += 1 - (gray / 255);
                                    count++;
                                }
                            }
                        }

                        result[i][j] = count > 0 ? sum / count : 0;
                    }
                }

                // Apply preprocessing
                return this.preprocessImage(result);
            }

            preprocessImage(imageData) {
                // Center the digit in the image
                const centered = this.centerDigit(imageData);

                // Apply smoothing
                const smoothed = this.smoothImage(centered);

                // Normalize intensity
                return this.normalizeImage(smoothed);
            }

            centerDigit(imageData) {
                // Find bounding box of the digit
                let minRow = 28, maxRow = -1, minCol = 28, maxCol = -1;

                for (let i = 0; i < 28; i++) {
                    for (let j = 0; j < 28; j++) {
                        if (imageData[i][j] > 0.1) {
                            minRow = Math.min(minRow, i);
                            maxRow = Math.max(maxRow, i);
                            minCol = Math.min(minCol, j);
                            maxCol = Math.max(maxCol, j);
                        }
                    }
                }

                // If no digit found, return original
                if (minRow > maxRow || minCol > maxCol) {
                    return imageData;
                }

                // Calculate center offset
                const digitCenterRow = (minRow + maxRow) / 2;
                const digitCenterCol = (minCol + maxCol) / 2;
                const imageCenterRow = 13.5;
                const imageCenterCol = 13.5;

                const offsetRow = Math.round(imageCenterRow - digitCenterRow);
                const offsetCol = Math.round(imageCenterCol - digitCenterCol);

                // Create centered image
                const centered = Array(28).fill().map(() => Array(28).fill(0));

                for (let i = 0; i < 28; i++) {
                    for (let j = 0; j < 28; j++) {
                        const srcRow = i - offsetRow;
                        const srcCol = j - offsetCol;

                        if (srcRow >= 0 && srcRow < 28 && srcCol >= 0 && srcCol < 28) {
                            centered[i][j] = imageData[srcRow][srcCol];
                        }
                    }
                }

                return centered;
            }

            smoothImage(imageData) {
                const smoothed = Array(28).fill().map(() => Array(28).fill(0));

                for (let i = 1; i < 27; i++) {
                    for (let j = 1; j < 27; j++) {
                        let sum = 0;
                        let count = 0;

                        // 3x3 smoothing kernel
                        for (let di = -1; di <= 1; di++) {
                            for (let dj = -1; dj <= 1; dj++) {
                                sum += imageData[i + di][j + dj];
                                count++;
                            }
                        }

                        smoothed[i][j] = sum / count;
                    }
                }

                // Copy edges
                for (let i = 0; i < 28; i++) {
                    smoothed[i][0] = imageData[i][0];
                    smoothed[i][27] = imageData[i][27];
                    smoothed[0][i] = imageData[0][i];
                    smoothed[27][i] = imageData[27][i];
                }

                return smoothed;
            }

            normalizeImage(imageData) {
                // Find max intensity
                let maxIntensity = 0;
                for (let i = 0; i < 28; i++) {
                    for (let j = 0; j < 28; j++) {
                        maxIntensity = Math.max(maxIntensity, imageData[i][j]);
                    }
                }

                // Normalize to 0-1 range
                if (maxIntensity > 0) {
                    const normalized = Array(28).fill().map(() => Array(28).fill(0));
                    for (let i = 0; i < 28; i++) {
                        for (let j = 0; j < 28; j++) {
                            normalized[i][j] = imageData[i][j] / maxIntensity;
                        }
                    }
                    return normalized;
                }

                return imageData;
            }

            classifyCurrentImage() {
                const imageData = this.getImageFromCanvas();

                // Show what the network sees
                this.visualizeNetworkInput(imageData);

                const prediction = this.network.predict(imageData);
                this.displayClassificationResult(prediction);

                console.log(`🔍 Classification: ${prediction.class} (${prediction.confidence.toFixed(1)}%)`);

                // Simple debugging information
                const analysis = this.network.analyzeImagePattern(imageData);

                console.log('📊 Pattern Analysis:');
                console.log('Detected patterns:', {
                    hasLoop: analysis.hasLoop,
                    hasVerticalLine: analysis.hasVerticalLine,
                    hasHorizontalLine: analysis.hasHorizontalLine,
                    hasDiagonal: analysis.hasDiagonal,
                    isSymmetric: analysis.isSymmetric
                });
                console.log('Region analysis:', {
                    topHeavy: analysis.topHeavy,
                    bottomHeavy: analysis.bottomHeavy,
                    leftHeavy: analysis.leftHeavy,
                    rightHeavy: analysis.rightHeavy,
                    centerDense: analysis.centerDense
                });
                console.log('Final Probabilities:', prediction.probabilities.map((p, i) => `${i}: ${(p*100).toFixed(1)}%`).join(', '));
                console.log('Top 3 candidates:',
                    prediction.probabilities
                        .map((p, i) => ({digit: i, prob: p}))
                        .sort((a, b) => b.prob - a.prob)
                        .slice(0, 3)
                        .map(item => `${item.digit}(${(item.prob*100).toFixed(1)}%)`)
                        .join(', ')
                );
            }

            visualizeNetworkInput(imageData) {
                const debugCanvas = this.debugCanvas;
                const debugCtx = this.debugCtx;

                // Clear debug canvas
                debugCtx.fillStyle = 'white';
                debugCtx.fillRect(0, 0, debugCanvas.width, debugCanvas.height);

                // Draw 28x28 image scaled to 84x84 (3x scale)
                const scale = 3;
                for (let i = 0; i < 28; i++) {
                    for (let j = 0; j < 28; j++) {
                        const intensity = imageData[i][j];
                        const gray = Math.floor((1 - intensity) * 255);
                        debugCtx.fillStyle = `rgb(${gray}, ${gray}, ${gray})`;
                        debugCtx.fillRect(j * scale, i * scale, scale, scale);
                    }
                }

                // Add grid lines for clarity
                debugCtx.strokeStyle = '#ddd';
                debugCtx.lineWidth = 0.5;
                for (let i = 0; i <= 28; i++) {
                    debugCtx.beginPath();
                    debugCtx.moveTo(i * scale, 0);
                    debugCtx.lineTo(i * scale, 28 * scale);
                    debugCtx.stroke();

                    debugCtx.beginPath();
                    debugCtx.moveTo(0, i * scale);
                    debugCtx.lineTo(28 * scale, i * scale);
                    debugCtx.stroke();
                }
            }

            displayClassificationResult(prediction) {
                document.getElementById('predictedDigit').textContent = prediction.class;
                document.getElementById('confidenceScore').textContent = prediction.confidence.toFixed(1) + '%';
                this.updateProbabilityBars(prediction.probabilities);
            }

            updateProbabilityBars(probabilities) {
                const container = document.getElementById('probabilityBars');
                container.innerHTML = '';

                for (let i = 0; i < probabilities.length; i++) {
                    const probability = probabilities[i] * 100;

                    const barDiv = document.createElement('div');
                    barDiv.className = 'prob-bar';

                    barDiv.innerHTML = `
                        <span class="prob-label">${i}</span>
                        <div class="prob-fill">
                            <div class="prob-value" style="width: ${probability}%"></div>
                        </div>
                        <span class="prob-percent">${probability.toFixed(1)}%</span>
                    `;

                    container.appendChild(barDiv);
                }
            }

            drawNetwork() {
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

                const currentTime = Date.now();

                // Draw animated connections
                this.drawAnimatedConnections();

                // Draw layers with live data
                this.drawAnimatedLayers();

                // Draw data flow particles
                this.drawDataFlowParticles();

                // Update animation state
                this.updateAnimationState(currentTime);

                console.log('✅ Network visualization drawn with animations');
            }

            drawAnimatedConnections() {
                const layers = this.layerPositions;

                for (let i = 0; i < layers.length - 1; i++) {
                    const from = layers[i];
                    const to = layers[i + 1];

                    // Base connection
                    this.ctx.strokeStyle = '#ddd';
                    this.ctx.lineWidth = 2;
                    this.ctx.beginPath();
                    this.ctx.moveTo(from.x + from.width, from.y + from.height / 2);
                    this.ctx.lineTo(to.x, to.y + to.height / 2);
                    this.ctx.stroke();

                    // Animated data flow
                    if (this.animationState.isAnimating && this.animationState.currentLayer === i) {
                        const progress = this.animationState.animationProgress;
                        const startX = from.x + from.width;
                        const startY = from.y + from.height / 2;
                        const endX = to.x;
                        const endY = to.y + to.height / 2;

                        const currentX = startX + (endX - startX) * progress;
                        const currentY = startY + (endY - startY) * progress;

                        // Draw flowing data
                        this.ctx.fillStyle = '#00ff00';
                        this.ctx.beginPath();
                        this.ctx.arc(currentX, currentY, 4, 0, 2 * Math.PI);
                        this.ctx.fill();

                        // Draw trail
                        for (let j = 1; j <= 5; j++) {
                            const trailProgress = Math.max(0, progress - j * 0.1);
                            const trailX = startX + (endX - startX) * trailProgress;
                            const trailY = startY + (endY - startY) * trailProgress;
                            const alpha = Math.max(0, 1 - j * 0.2);

                            this.ctx.fillStyle = `rgba(0, 255, 0, ${alpha})`;
                            this.ctx.beginPath();
                            this.ctx.arc(trailX, trailY, 2, 0, 2 * Math.PI);
                            this.ctx.fill();
                        }
                    }
                }
            }

            drawAnimatedLayers() {
                const layers = [
                    { ...this.layerPositions[0], label: 'Input\n28×28', color: '#4CAF50' },
                    { ...this.layerPositions[1], label: 'Conv\n8 Filters', color: '#2196F3' },
                    { ...this.layerPositions[2], label: 'Pool\n2×2', color: '#FF9800' },
                    { ...this.layerPositions[3], label: 'Dense\n64', color: '#F44336' },
                    { ...this.layerPositions[4], label: 'Output\n10', color: '#9C27B0' }
                ];

                for (let i = 0; i < layers.length; i++) {
                    const layer = layers[i];

                    // Calculate activation intensity
                    let intensity = 0.3;
                    if (this.network.layerOutputs && this.getLayerActivation(layer.type)) {
                        intensity = Math.min(1, this.getLayerActivation(layer.type) * 2);
                    }

                    // Animate during processing
                    if (this.animationState.isAnimating && this.animationState.currentLayer === i) {
                        intensity = Math.max(intensity, 0.8 + 0.2 * Math.sin(Date.now() * 0.01));
                    }

                    // Draw layer background with intensity
                    const alpha = Math.floor(intensity * 100).toString(16).padStart(2, '0');
                    this.ctx.fillStyle = layer.color + alpha;
                    this.ctx.fillRect(layer.x, layer.y, layer.width, layer.height);

                    // Draw layer border
                    this.ctx.strokeStyle = layer.color;
                    this.ctx.lineWidth = this.animationState.currentLayer === i ? 3 : 2;
                    this.ctx.strokeRect(layer.x, layer.y, layer.width, layer.height);

                    // Draw mini feature maps for conv and pool layers
                    if (layer.type === 'conv' || layer.type === 'pool') {
                        this.drawMiniFeatureMaps(layer, intensity);
                    }

                    // Draw activation bars for dense and output layers
                    if (layer.type === 'dense' || layer.type === 'output') {
                        this.drawActivationBars(layer, intensity);
                    }

                    // Draw labels
                    this.ctx.fillStyle = '#333';
                    this.ctx.font = 'bold 12px Arial';
                    this.ctx.textAlign = 'center';
                    const lines = layer.label.split('\n');
                    for (let j = 0; j < lines.length; j++) {
                        this.ctx.fillText(
                            lines[j],
                            layer.x + layer.width / 2,
                            layer.y - 10 + j * 14
                        );
                    }

                    // Draw live values
                    if (this.network.layerOutputs) {
                        this.drawLiveValues(layer, i);
                    }
                }
            }

            drawMiniFeatureMaps(layer, intensity) {
                const mapSize = 8;
                const spacing = 2;
                const mapsPerRow = layer.type === 'conv' ? 4 : 2;
                const totalMaps = layer.type === 'conv' ? 8 : 4;

                for (let i = 0; i < totalMaps; i++) {
                    const row = Math.floor(i / mapsPerRow);
                    const col = i % mapsPerRow;

                    const x = layer.x + 10 + col * (mapSize + spacing);
                    const y = layer.y + 20 + row * (mapSize + spacing);

                    // Random activation pattern
                    const activation = Math.random() * intensity;
                    const alpha = Math.floor(activation * 255).toString(16).padStart(2, '0');

                    this.ctx.fillStyle = '#ffffff' + alpha;
                    this.ctx.fillRect(x, y, mapSize, mapSize);

                    this.ctx.strokeStyle = '#666';
                    this.ctx.lineWidth = 1;
                    this.ctx.strokeRect(x, y, mapSize, mapSize);
                }
            }

            drawActivationBars(layer, intensity) {
                const barCount = layer.type === 'dense' ? 8 : 10;
                const barWidth = 3;
                const barSpacing = 1;
                const maxBarHeight = layer.height - 20;

                for (let i = 0; i < barCount; i++) {
                    const x = layer.x + 5 + i * (barWidth + barSpacing);
                    const activation = Math.random() * intensity;
                    const barHeight = activation * maxBarHeight;

                    const y = layer.y + layer.height - 10 - barHeight;

                    // Color based on activation
                    const hue = activation * 120; // Green to red
                    this.ctx.fillStyle = `hsl(${hue}, 70%, 50%)`;
                    this.ctx.fillRect(x, y, barWidth, barHeight);
                }
            }

            drawLiveValues(layer, layerIndex) {
                if (!this.network.layerOutputs) return;

                let value = 0;
                switch (layer.type) {
                    case 'input':
                        if (this.network.layerOutputs.input) {
                            const input = this.network.layerOutputs.input;
                            value = input.flat().reduce((a, b) => a + b, 0) / (28 * 28);
                        }
                        break;
                    case 'conv':
                        if (this.network.layerOutputs.conv) {
                            value = this.network.layerOutputs.conv[0] ?
                                   this.network.layerOutputs.conv[0].flat().reduce((a, b) => a + b, 0) / 676 : 0;
                        }
                        break;
                    case 'pool':
                        if (this.network.layerOutputs.pool) {
                            value = this.network.layerOutputs.pool[0] ?
                                   this.network.layerOutputs.pool[0].flat().reduce((a, b) => a + b, 0) / 169 : 0;
                        }
                        break;
                    case 'dense':
                        if (this.network.layerOutputs.dense) {
                            value = this.network.layerOutputs.dense.reduce((a, b) => a + b, 0) / 64;
                        }
                        break;
                    case 'output':
                        if (this.network.layerOutputs.output) {
                            value = Math.max(...this.network.layerOutputs.output);
                        }
                        break;
                }

                // Draw live value
                this.ctx.fillStyle = '#000';
                this.ctx.font = '10px Arial';
                this.ctx.textAlign = 'center';
                this.ctx.fillText(
                    value.toFixed(3),
                    layer.x + layer.width / 2,
                    layer.y + layer.height + 15
                );
            }

            drawDataFlowParticles() {
                // Draw floating particles during training
                if (this.network.isTraining) {
                    for (let i = 0; i < 10; i++) {
                        const x = 50 + Math.random() * 700;
                        const y = 100 + Math.random() * 200;
                        const alpha = Math.random() * 0.5;

                        this.ctx.fillStyle = `rgba(0, 150, 255, ${alpha})`;
                        this.ctx.beginPath();
                        this.ctx.arc(x, y, 2, 0, 2 * Math.PI);
                        this.ctx.fill();
                    }
                }
            }

            updateAnimationState(currentTime) {
                if (this.animationState.isAnimating) {
                    const deltaTime = currentTime - this.animationState.lastUpdateTime;
                    this.animationState.animationProgress += deltaTime * 0.002;

                    if (this.animationState.animationProgress >= 1) {
                        this.animationState.animationProgress = 0;
                        this.animationState.currentLayer++;

                        if (this.animationState.currentLayer >= this.layerPositions.length - 1) {
                            this.animationState.currentLayer = 0;
                            this.animationState.isAnimating = false;
                        }
                    }
                }

                this.animationState.lastUpdateTime = currentTime;

                // Continue animation loop
                if (this.animationState.isAnimating || this.network.isTraining) {
                    requestAnimationFrame(() => this.drawNetwork());
                }
            }

            getLayerActivation(layerType) {
                if (!this.network.layerOutputs) return 0;

                switch (layerType) {
                    case 'input':
                        return this.network.layerOutputs.input ?
                               this.network.layerOutputs.input.flat().reduce((a, b) => a + b, 0) / (28 * 28) : 0;
                    case 'conv':
                        return this.network.layerOutputs.conv ?
                               this.network.layerOutputs.conv[0].flat().reduce((a, b) => a + b, 0) / 676 : 0;
                    case 'pool':
                        return this.network.layerOutputs.pool ?
                               this.network.layerOutputs.pool[0].flat().reduce((a, b) => a + b, 0) / 169 : 0;
                    case 'dense':
                        return this.network.layerOutputs.dense ?
                               this.network.layerOutputs.dense.reduce((a, b) => a + b, 0) / 64 : 0;
                    case 'output':
                        return this.network.layerOutputs.output ?
                               Math.max(...this.network.layerOutputs.output) : 0;
                    default:
                        return 0;
                }
            }

            startForwardPassAnimation() {
                this.animationState.isAnimating = true;
                this.animationState.currentLayer = 0;
                this.animationState.animationProgress = 0;
                this.animationState.lastUpdateTime = Date.now();

                // Start animation loop
                requestAnimationFrame(() => this.drawNetwork());
            }

            handleMouseMove(e) {
                const rect = this.canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                // Check which layer is being hovered
                const hoveredLayer = this.getLayerAt(x, y);
                if (hoveredLayer) {
                    this.showLayerTooltip(hoveredLayer, e.clientX, e.clientY);
                } else {
                    this.hideTooltip();
                }
            }

            getLayerAt(x, y) {
                const layers = [
                    { type: 'input', x: 100, y: 150, width: 80, height: 100, index: 0 },
                    { type: 'conv', x: 250, y: 120, width: 100, height: 160, index: 1 },
                    { type: 'pool', x: 420, y: 150, width: 80, height: 100, index: 2 },
                    { type: 'dense', x: 570, y: 170, width: 60, height: 60, index: 3 },
                    { type: 'output', x: 700, y: 175, width: 60, height: 50, index: 4 }
                ];

                for (const layer of layers) {
                    if (x >= layer.x && x <= layer.x + layer.width &&
                        y >= layer.y && y <= layer.y + layer.height) {
                        return layer;
                    }
                }
                return null;
            }

            showLayerTooltip(layer, clientX, clientY) {
                const layerTitles = {
                    'input': 'Input Layer',
                    'conv': 'Convolutional Layer',
                    'pool': 'Max Pooling Layer',
                    'dense': 'Dense Layer',
                    'output': 'Output Layer'
                };

                this.tooltipTitle.textContent = layerTitles[layer.type];

                let content = this.getLayerTooltipContent(layer.type);
                this.tooltipContent.innerHTML = content;

                this.showTooltip(clientX, clientY);
            }

            getLayerTooltipContent(layerType) {
                switch (layerType) {
                    case 'input':
                        return this.getInputTooltip();
                    case 'conv':
                        return this.getConvTooltip();
                    case 'pool':
                        return this.getPoolTooltip();
                    case 'dense':
                        return this.getDenseTooltip();
                    case 'output':
                        return this.getOutputTooltip();
                    default:
                        return '<div>Layer information not available</div>';
                }
            }

            getInputTooltip() {
                let content = '<div><strong>Function:</strong> Receives input image data</div>';
                content += '<div><strong>Shape:</strong> 28×28 pixels</div>';
                content += '<div><strong>Values:</strong> Pixel intensities (0-1)</div>';

                if (this.network.layerOutputs.input) {
                    const input = this.network.layerOutputs.input;
                    const samplePixel = input[14] ? input[14][14] || 0 : 0;

                    content += '<div style="margin-top: 8px; border-top: 1px solid #444; padding-top: 8px;"><strong>Sample Calculation:</strong></div>';
                    content += '<div class="math-step">';
                    content += '<div class="step-title">Pixel Normalization</div>';
                    content += '<div class="math-formula">normalized = pixel_value / 255</div>';
                    content += '<div>Center pixel (14,14): <span class="tooltip-value">' + samplePixel.toFixed(4) + '</span></div>';
                    content += '</div>';
                }

                return content;
            }

            getConvTooltip() {
                let content = '<div><strong>Function:</strong> Feature detection using filters</div>';
                content += '<div><strong>Filters:</strong> 8 different feature detectors</div>';
                content += '<div><strong>Filter Size:</strong> 3×3 kernels</div>';
                content += '<div><strong>Output:</strong> 26×26 feature maps</div>';

                if (this.network.calculations.convolution.length > 0) {
                    const calc = this.network.calculations.convolution[0];
                    const sample = calc.sampleCalculations[0];

                    content += '<div style="margin-top: 8px; border-top: 1px solid #444; padding-top: 8px;"><strong>Mathematical Process:</strong></div>';
                    content += '<div class="math-step">';
                    content += '<div class="step-title">Step 1: Convolution Operation</div>';
                    content += '<div class="math-formula">output[i,j] = Σ(filter[m,n] × input[i+m,j+n])</div>';
                    content += '<div>Sample position (5,5):</div>';

                    for (let i = 0; i < 3; i++) {
                        const idx = i * 3;
                        content += '<div>';
                        for (let j = 0; j < 3; j++) {
                            const weight = sample.weights[idx + j];
                            const input = sample.inputs[idx + j];
                            const product = weight * input;
                            content += `<span class="tooltip-value">${weight.toFixed(2)}</span>×<span class="tooltip-value">${input.toFixed(2)}</span> `;
                        }
                        content += '</div>';
                    }

                    content += '<div class="step-result">Convolution Sum = <span class="tooltip-value">' + sample.convSum.toFixed(4) + '</span></div>';
                    content += '</div>';

                    content += '<div class="math-step">';
                    content += '<div class="step-title">Step 2: ReLU Activation</div>';
                    content += '<div class="math-formula">activation = max(0, convolution_sum)</div>';
                    content += '<div class="step-result">Final Output = <span class="tooltip-value">' + sample.activation.toFixed(4) + '</span></div>';
                    content += '</div>';
                }

                return content;
            }

            getPoolTooltip() {
                let content = '<div><strong>Function:</strong> Spatial dimension reduction</div>';
                content += '<div><strong>Pool Size:</strong> 2×2 windows</div>';
                content += '<div><strong>Operation:</strong> Maximum value selection</div>';
                content += '<div><strong>Output:</strong> 13×13 feature maps</div>';

                if (this.network.calculations.pooling.length > 0) {
                    const calc = this.network.calculations.pooling[0];
                    const sample = calc.sampleCalculations[0];

                    content += '<div style="margin-top: 8px; border-top: 1px solid #444; padding-top: 8px;"><strong>Mathematical Process:</strong></div>';
                    content += '<div class="math-step">';
                    content += '<div class="step-title">Max Pooling Operation</div>';
                    content += '<div class="math-formula">output = max(pool_region)</div>';
                    content += '<div>Sample region (4,4) to (5,5):</div>';
                    content += '<div>Values: [';
                    for (let i = 0; i < sample.values.length; i++) {
                        content += '<span class="tooltip-value">' + sample.values[i].toFixed(3) + '</span>';
                        if (i < sample.values.length - 1) content += ', ';
                    }
                    content += ']</div>';
                    content += '<div class="step-result">Maximum = <span class="tooltip-value">' + sample.maxValue.toFixed(4) + '</span></div>';
                    content += '</div>';
                }

                return content;
            }

            getDenseTooltip() {
                let content = '<div><strong>Function:</strong> High-level feature learning</div>';
                content += '<div><strong>Units:</strong> 64 fully connected neurons</div>';
                content += '<div><strong>Input:</strong> 1352 flattened features</div>';
                content += '<div><strong>Activation:</strong> ReLU function</div>';

                if (this.network.calculations.dense.length > 0) {
                    const calc = this.network.calculations.dense[0];

                    content += '<div style="margin-top: 8px; border-top: 1px solid #444; padding-top: 8px;"><strong>Mathematical Process:</strong></div>';
                    content += '<div class="math-step">';
                    content += '<div class="step-title">Step 1: Weighted Sum (Sample)</div>';
                    content += '<div class="math-formula">z = Σ(w[i] × input[i]) + bias</div>';
                    content += '<div>Sample connections (first 9 of ' + calc.totalConnections + '):</div>';

                    for (let i = 0; i < Math.min(3, calc.sampleInputs.length); i++) {
                        const weight = calc.sampleWeights[i];
                        const input = calc.sampleInputs[i];
                        const product = weight * input;
                        content += `<div><span class="tooltip-value">${weight.toFixed(3)}</span> × <span class="tooltip-value">${input.toFixed(3)}</span> = <span class="tooltip-value">${product.toFixed(3)}</span></div>`;
                    }
                    content += '<div>... (+ ' + (calc.totalConnections - 3) + ' more connections)</div>';

                    content += '<div class="step-result">Weighted Sum = <span class="tooltip-value">' + calc.weightedSum.toFixed(4) + '</span></div>';
                    content += '</div>';

                    content += '<div class="math-step">';
                    content += '<div class="step-title">Step 2: Add Bias</div>';
                    content += '<div class="math-formula">pre_activation = weighted_sum + bias</div>';
                    content += '<div>' + calc.weightedSum.toFixed(4) + ' + ' + calc.bias.toFixed(4) + ' = ' + calc.preActivation.toFixed(4) + '</div>';
                    content += '</div>';

                    content += '<div class="math-step">';
                    content += '<div class="step-title">Step 3: ReLU Activation</div>';
                    content += '<div class="math-formula">activation = max(0, pre_activation)</div>';
                    content += '<div class="step-result">Final Output = <span class="tooltip-value">' + calc.activation.toFixed(4) + '</span></div>';
                    content += '</div>';
                }

                return content;
            }

            getOutputTooltip() {
                let content = '<div><strong>Function:</strong> Classification decision</div>';
                content += '<div><strong>Classes:</strong> 10 digit categories (0-9)</div>';
                content += '<div><strong>Input:</strong> 64 dense features</div>';
                content += '<div><strong>Activation:</strong> Softmax function</div>';

                if (this.network.calculations.softmax.length > 0) {
                    const calc = this.network.calculations.softmax[0];

                    content += '<div style="margin-top: 8px; border-top: 1px solid #444; padding-top: 8px;"><strong>Mathematical Process:</strong></div>';
                    content += '<div class="math-step">';
                    content += '<div class="step-title">Step 1: Linear Transformation</div>';
                    content += '<div class="math-formula">logit = Σ(w[i] × dense[i]) + bias</div>';
                    content += '<div>Sample connections for class 0:</div>';

                    for (let i = 0; i < Math.min(3, calc.sampleInputs.length); i++) {
                        const weight = calc.sampleWeights[i];
                        const input = calc.sampleInputs[i];
                        const product = weight * input;
                        content += `<div><span class="tooltip-value">${weight.toFixed(3)}</span> × <span class="tooltip-value">${input.toFixed(3)}</span> = <span class="tooltip-value">${product.toFixed(3)}</span></div>`;
                    }

                    content += '<div class="step-result">Logit = <span class="tooltip-value">' + calc.logit.toFixed(4) + '</span></div>';
                    content += '</div>';

                    content += '<div class="math-step">';
                    content += '<div class="step-title">Step 2: Softmax Normalization</div>';
                    content += '<div class="math-formula">P(class) = e^(logit) / Σ(e^(all_logits))</div>';
                    content += '<div>e^(' + calc.logit.toFixed(4) + ') = ' + calc.expLogit.toFixed(4) + '</div>';
                    content += '<div>Sum of all exponentials = ' + calc.sumExp.toFixed(4) + '</div>';
                    content += '<div class="step-result">Probability = <span class="tooltip-value">' + (calc.probability * 100).toFixed(2) + '%</span></div>';
                    content += '</div>';
                }

                return content;
            }

            showTooltip(clientX, clientY) {
                this.tooltip.style.left = (clientX + 10) + 'px';
                this.tooltip.style.top = (clientY - 10) + 'px';
                this.tooltip.classList.add('visible');
            }

            hideTooltip() {
                this.tooltip.classList.remove('visible');
            }

            startTraining() {
                if (this.network.isTraining) {
                    this.stopTraining();
                    return;
                }

                this.network.isTraining = true;
                document.getElementById('startCNNTraining').textContent = 'Stop Training';

                this.trainStep();
                console.log('🎯 Training started');
            }

            trainStep() {
                if (!this.network.isTraining) return;

                const result = this.network.simulateTraining();

                document.getElementById('cnnEpochCount').textContent = result.epoch;
                document.getElementById('cnnLossValue').textContent = result.loss.toFixed(3);
                document.getElementById('cnnAccuracyValue').textContent = result.accuracy.toFixed(0) + '%';
                document.getElementById('valAccuracyValue').textContent = result.valAccuracy.toFixed(0) + '%';

                // Re-classify current image to show improved accuracy
                this.classifyCurrentImage();

                // Trigger animation during training
                if (!this.animationState.isAnimating) {
                    this.startForwardPassAnimation();
                }

                if (result.epoch >= 100) {
                    this.stopTraining();
                    return;
                }

                this.trainingInterval = setTimeout(() => {
                    this.trainStep();
                }, 100);
            }

            stopTraining() {
                this.network.isTraining = false;
                document.getElementById('startCNNTraining').textContent = 'Start Training';

                if (this.trainingInterval) {
                    clearTimeout(this.trainingInterval);
                    this.trainingInterval = null;
                }

                console.log('⏹️ Training stopped');
            }

            togglePause() {
                if (this.network.isTraining) {
                    this.stopTraining();
                    console.log('⏸️ Training paused');
                } else {
                    this.startTraining();
                    console.log('▶️ Training resumed');
                }
            }

            demonstrateForwardPass() {
                this.startForwardPassAnimation();
                this.classifyCurrentImage();
                console.log('⚡ Forward pass demonstrated with animation');
            }

            resetNetwork() {
                this.stopTraining();
                this.network = new SimpleCNN();

                document.getElementById('cnnEpochCount').textContent = '0';
                document.getElementById('cnnLossValue').textContent = '2.303';
                document.getElementById('cnnAccuracyValue').textContent = '10%';

                this.loadSampleImage('digit0');
                console.log('🔄 Network reset');
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            try {
                window.cnnApp = new CNNApp();
                console.log('🎉 CNN Visualizer loaded successfully!');
                console.log('Try drawing on the canvas or selecting different digits!');
            } catch (error) {
                console.error('❌ Error loading CNN Visualizer:', error);
            }
        });
    </script>
</body>
</html>
