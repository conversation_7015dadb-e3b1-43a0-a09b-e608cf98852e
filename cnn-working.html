<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CNN Visualizer - Working Version</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }

        header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .main-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .cnn-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            position: relative;
        }

        #cnnCanvas {
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #fafafa;
            display: block;
            margin: 0 auto;
            cursor: crosshair;
        }

        .test-prediction-main {
            margin: 20px 0 10px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .test-prediction-main h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.1rem;
            text-align: center;
        }

        .image-test {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .input-controls {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .drawing-area {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        #drawingCanvas {
            border: 2px solid #ddd;
            border-radius: 8px;
            background: white;
            cursor: crosshair;
        }

        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .classification-result {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .predicted-class {
            text-align: center;
            margin-bottom: 10px;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .classification-value {
            font-size: 2rem;
            padding: 8px 16px;
            border-radius: 8px;
            margin-left: 8px;
            background: #e8f5e8;
            color: #2e7d32;
            border: 2px solid #2e7d32;
        }

        .animation-controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }

        .controls-panel {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            height: fit-content;
        }

        .control-group {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e0e0e0;
        }

        .control-group h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        select, input[type="range"] {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .progress-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .progress-info div {
            margin: 5px 0;
            font-weight: 500;
        }

        #probabilityBars {
            display: flex;
            flex-direction: column;
            gap: 3px;
        }

        .prob-bar {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.8rem;
        }

        .prob-label {
            width: 20px;
            font-weight: bold;
        }

        .prob-fill {
            flex: 1;
            height: 16px;
            background: #f0f0f0;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
        }

        .prob-value {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 8px;
            transition: width 0.3s ease;
        }

        .prob-percent {
            width: 40px;
            text-align: right;
            font-size: 0.7rem;
            color: #666;
        }

        /* Tooltip styles */
        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.95);
            color: white;
            padding: 12px 16px;
            border-radius: 10px;
            font-size: 12px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            pointer-events: none;
            z-index: 1000;
            max-width: 400px;
            min-width: 280px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
            opacity: 0;
            transition: opacity 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .tooltip.visible {
            opacity: 1;
        }

        .tooltip-title {
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
            border-bottom: 1px solid #667eea;
            padding-bottom: 3px;
        }

        .tooltip-content {
            line-height: 1.4;
        }

        .tooltip-value {
            color: #4CAF50;
            font-weight: bold;
        }

        .tooltip-negative {
            color: #f44336;
        }

        .math-step {
            background: rgba(255, 255, 255, 0.1);
            padding: 6px 8px;
            margin: 4px 0;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            border-left: 3px solid #667eea;
        }

        .math-formula {
            font-family: 'Courier New', monospace;
            background: rgba(102, 126, 234, 0.2);
            padding: 4px 6px;
            border-radius: 3px;
            margin: 2px 0;
            font-weight: bold;
        }

        .step-title {
            font-weight: bold;
            color: #667eea;
            margin-bottom: 3px;
        }

        .step-result {
            border-top: 1px solid #666;
            margin: 3px 0;
            padding-top: 3px;
            font-weight: bold;
        }

        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .image-test {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>🔍 CNN Visualizer</h1>
            <p>Interactive Convolutional Neural Network Demonstration</p>
        </header>

        <div class="main-content">
            <div class="cnn-container">
                <canvas id="cnnCanvas" width="800" height="400"></canvas>
                <div id="cnnTooltip" class="tooltip">
                    <div class="tooltip-title"></div>
                    <div class="tooltip-content"></div>
                </div>
                
                <div class="control-group test-prediction-main">
                    <h3>🖼️ Test Image Classification</h3>
                    <div class="image-test">
                        <div class="input-controls">
                            <div class="image-selector">
                                <label>Select Test Image:</label>
                                <select id="imageSelect">
                                    <option value="digit0">Digit 0</option>
                                    <option value="digit1">Digit 1</option>
                                    <option value="digit2">Digit 2</option>
                                    <option value="digit3">Digit 3</option>
                                    <option value="digit4">Digit 4</option>
                                    <option value="digit5">Digit 5</option>
                                    <option value="digit6">Digit 6</option>
                                    <option value="digit7">Digit 7</option>
                                    <option value="digit8">Digit 8</option>
                                    <option value="digit9">Digit 9</option>
                                </select>
                            </div>
                            <div class="drawing-area">
                                <label>Or Draw Your Own:</label>
                                <canvas id="drawingCanvas" width="140" height="140"></canvas>
                                <button id="clearDrawing">Clear</button>
                            </div>
                            <button id="classifyImage">Classify Image</button>
                        </div>
                        <div class="classification-result">
                            <div class="prediction-output">
                                <div class="predicted-class">
                                    <span class="classification-label">Predicted Digit:</span>
                                    <span id="predictedDigit" class="classification-value">-</span>
                                </div>
                                <div class="confidence-score">
                                    Confidence: <span id="confidenceScore">0.0%</span>
                                </div>
                                <div class="probability-distribution">
                                    <h4>Probability Distribution:</h4>
                                    <div id="probabilityBars" class="probability-bars"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="animation-controls">
                    <button id="startCNNTraining">Start Training</button>
                    <button id="forwardPassCNN">Forward Pass</button>
                    <button id="resetCNN">Reset</button>
                    <button id="pauseResumeCNN">Pause</button>
                </div>
            </div>

            <div class="controls-panel">
                <div class="control-group">
                    <h3>🎛️ Fine-tuning Parameters</h3>
                    <label>
                        Learning Rate:
                        <input type="range" id="learningRate" min="0.001" max="0.1" step="0.001" value="0.01">
                        <span id="lrValue">0.01</span>
                    </label>
                    <label>
                        Pattern Sensitivity:
                        <input type="range" id="patternSensitivity" min="0.1" max="2.0" step="0.1" value="1.0">
                        <span id="patternValue">1.0</span>
                    </label>
                    <label>
                        Feature Strength:
                        <input type="range" id="featureStrength" min="0.5" max="3.0" step="0.1" value="1.5">
                        <span id="featureValue">1.5</span>
                    </label>
                    <label>
                        Noise Reduction:
                        <input type="range" id="noiseReduction" min="0.0" max="1.0" step="0.1" value="0.3">
                        <span id="noiseValue">0.3</span>
                    </label>
                    <label>
                        Training Intensity:
                        <input type="range" id="trainingIntensity" min="0.1" max="2.0" step="0.1" value="1.0">
                        <span id="intensityValue">1.0</span>
                    </label>
                </div>

                <div class="control-group">
                    <h3>Training Progress</h3>
                    <div class="progress-info">
                        <div>Epoch: <span id="cnnEpochCount">0</span></div>
                        <div>Loss: <span id="cnnLossValue">2.303</span></div>
                        <div>Accuracy: <span id="cnnAccuracyValue">10%</span></div>
                        <div>Validation: <span id="valAccuracyValue">10%</span></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        console.log('🚀 Starting CNN Visualizer...');
        
        // Simple CNN implementation
        class SimpleCNN {
            constructor() {
                this.epoch = 0;
                this.lossHistory = [];
                this.isTraining = false;

                // Store layer outputs for visualization
                this.layerOutputs = {
                    input: null,
                    conv: null,
                    pool: null,
                    flatten: null,
                    dense: null,
                    output: null
                };

                // Store calculation details for tooltips
                this.calculations = {
                    convolution: [],
                    pooling: [],
                    dense: [],
                    softmax: []
                };

                // Network parameters
                this.config = {
                    inputShape: [28, 28],
                    convFilters: 8,
                    filterSize: 3,
                    poolSize: 2,
                    denseUnits: 64,
                    outputClasses: 10
                };

                // Fine-tuning parameters
                this.tuningParams = {
                    learningRate: 0.01,
                    patternSensitivity: 1.0,
                    featureStrength: 1.5,
                    noiseReduction: 0.3,
                    trainingIntensity: 1.0
                };

                // Training progress affects accuracy
                this.trainingProgress = 0; // 0 to 1
                this.baseAccuracy = 0.1; // Starting accuracy

                // Digit pattern templates for better recognition
                this.digitPatterns = this.initializeDigitPatterns();
            }

            initializeDigitPatterns() {
                return {
                    0: { // Circle pattern
                        center: true,
                        edges: true,
                        corners: false,
                        horizontal: false,
                        vertical: false,
                        diagonal: false
                    },
                    1: { // Vertical line
                        center: false,
                        edges: false,
                        corners: false,
                        horizontal: false,
                        vertical: true,
                        diagonal: false
                    },
                    2: { // Horizontal segments
                        center: false,
                        edges: true,
                        corners: true,
                        horizontal: true,
                        vertical: false,
                        diagonal: false
                    },
                    3: { // Curved segments
                        center: false,
                        edges: true,
                        corners: false,
                        horizontal: true,
                        vertical: false,
                        diagonal: false
                    },
                    4: { // Vertical + horizontal
                        center: false,
                        edges: false,
                        corners: true,
                        horizontal: true,
                        vertical: true,
                        diagonal: false
                    },
                    5: { // Mixed segments
                        center: false,
                        edges: true,
                        corners: true,
                        horizontal: true,
                        vertical: false,
                        diagonal: false
                    },
                    6: { // Curved with loop
                        center: true,
                        edges: true,
                        corners: false,
                        horizontal: true,
                        vertical: false,
                        diagonal: false
                    },
                    7: { // Diagonal line
                        center: false,
                        edges: false,
                        corners: true,
                        horizontal: true,
                        vertical: false,
                        diagonal: true
                    },
                    8: { // Double loop
                        center: true,
                        edges: true,
                        corners: false,
                        horizontal: true,
                        vertical: false,
                        diagonal: false
                    },
                    9: { // Loop with tail
                        center: true,
                        edges: true,
                        corners: false,
                        horizontal: false,
                        vertical: true,
                        diagonal: false
                    }
                };
            }
            
            predict(imageData) {
                // Store input
                this.layerOutputs.input = imageData;

                // Extract features from image
                const features = this.extractImageFeatures(imageData);

                // Simulate convolution layer
                const convOutputs = this.simulateConvolution(imageData, features);
                this.layerOutputs.conv = convOutputs;

                // Simulate pooling layer
                const poolOutputs = this.simulatePooling(convOutputs);
                this.layerOutputs.pool = poolOutputs;

                // Simulate flatten
                const flattened = this.simulateFlatten(poolOutputs);
                this.layerOutputs.flatten = flattened;

                // Simulate dense layer
                const denseOutputs = this.simulateDense(flattened);
                this.layerOutputs.dense = denseOutputs;

                // Intelligent pattern matching for output
                const outputProbs = this.intelligentClassification(features, denseOutputs);
                this.layerOutputs.output = outputProbs;

                const predictedClass = outputProbs.indexOf(Math.max(...outputProbs));
                const confidence = Math.max(...outputProbs) * 100;

                return {
                    class: predictedClass,
                    confidence: confidence,
                    probabilities: outputProbs
                };
            }

            extractImageFeatures(imageData) {
                const height = imageData.length;
                const width = imageData[0].length;

                // Advanced feature extraction
                const features = {
                    // Shape features
                    topDensity: 0,
                    middleDensity: 0,
                    bottomDensity: 0,
                    leftDensity: 0,
                    rightDensity: 0,
                    centerDensity: 0,

                    // Line features
                    horizontalLines: 0,
                    verticalLines: 0,
                    diagonalLines: 0,

                    // Structural features
                    loops: 0,
                    openings: 0,
                    endpoints: 0,
                    intersections: 0,

                    // Geometric features
                    aspectRatio: 0,
                    symmetryVertical: 0,
                    symmetryHorizontal: 0,
                    compactness: 0,

                    // Specific digit patterns
                    circularPattern: 0,
                    straightPattern: 0,
                    curvedPattern: 0
                };

                // Calculate region densities
                const regions = this.calculateRegionDensities(imageData);
                features.topDensity = regions.top;
                features.middleDensity = regions.middle;
                features.bottomDensity = regions.bottom;
                features.leftDensity = regions.left;
                features.rightDensity = regions.right;
                features.centerDensity = regions.center;

                // Detect lines and patterns
                const lineFeatures = this.detectLines(imageData);
                features.horizontalLines = lineFeatures.horizontal;
                features.verticalLines = lineFeatures.vertical;
                features.diagonalLines = lineFeatures.diagonal;

                // Detect structural elements
                const structural = this.detectStructuralFeatures(imageData);
                features.loops = structural.loops;
                features.openings = structural.openings;
                features.endpoints = structural.endpoints;
                features.intersections = structural.intersections;

                // Calculate geometric properties
                features.aspectRatio = this.calculateAspectRatio(imageData);
                features.symmetryVertical = this.calculateVerticalSymmetry(imageData);
                features.symmetryHorizontal = this.calculateHorizontalSymmetry(imageData);
                features.compactness = this.calculateCompactness(imageData);

                // Detect specific patterns
                features.circularPattern = this.detectCircularPattern(imageData);
                features.straightPattern = this.detectStraightPattern(imageData);
                features.curvedPattern = this.detectCurvedPattern(imageData);

                return features;
            }

            calculateRegionDensities(imageData) {
                const height = imageData.length;
                const width = imageData[0].length;

                let top = 0, middle = 0, bottom = 0;
                let left = 0, right = 0, center = 0;

                for (let i = 0; i < height; i++) {
                    for (let j = 0; j < width; j++) {
                        const pixel = imageData[i][j];

                        // Vertical regions
                        if (i < height / 3) top += pixel;
                        else if (i < 2 * height / 3) middle += pixel;
                        else bottom += pixel;

                        // Horizontal regions
                        if (j < width / 3) left += pixel;
                        else if (j < 2 * width / 3) center += pixel;
                        else right += pixel;
                    }
                }

                const totalArea = height * width / 3;
                return {
                    top: top / totalArea,
                    middle: middle / totalArea,
                    bottom: bottom / totalArea,
                    left: left / totalArea,
                    right: right / totalArea,
                    center: center / totalArea
                };
            }

            detectLines(imageData) {
                const height = imageData.length;
                const width = imageData[0].length;
                let horizontal = 0, vertical = 0, diagonal = 0;

                // Detect horizontal lines
                for (let i = 1; i < height - 1; i++) {
                    let lineStrength = 0;
                    for (let j = 1; j < width - 1; j++) {
                        if (imageData[i][j] > 0.3) {
                            lineStrength++;
                        }
                    }
                    if (lineStrength > width * 0.4) {
                        horizontal += lineStrength / width;
                    }
                }

                // Detect vertical lines
                for (let j = 1; j < width - 1; j++) {
                    let lineStrength = 0;
                    for (let i = 1; i < height - 1; i++) {
                        if (imageData[i][j] > 0.3) {
                            lineStrength++;
                        }
                    }
                    if (lineStrength > height * 0.4) {
                        vertical += lineStrength / height;
                    }
                }

                // Detect diagonal lines
                for (let offset = -10; offset <= 10; offset++) {
                    let diagStrength = 0;
                    let count = 0;
                    for (let i = 0; i < height; i++) {
                        const j = i + offset + width / 2 - height / 2;
                        if (j >= 0 && j < width) {
                            if (imageData[i][Math.floor(j)] > 0.3) {
                                diagStrength++;
                            }
                            count++;
                        }
                    }
                    if (count > 0 && diagStrength / count > 0.4) {
                        diagonal += diagStrength / count;
                    }
                }

                return { horizontal, vertical, diagonal };
            }

            detectStructuralFeatures(imageData) {
                const height = imageData.length;
                const width = imageData[0].length;
                let loops = 0, openings = 0, endpoints = 0, intersections = 0;

                // Simple loop detection (enclosed areas)
                for (let i = 2; i < height - 2; i++) {
                    for (let j = 2; j < width - 2; j++) {
                        if (imageData[i][j] < 0.2) { // Empty center
                            let surrounding = 0;
                            for (let di = -2; di <= 2; di++) {
                                for (let dj = -2; dj <= 2; dj++) {
                                    if (Math.abs(di) + Math.abs(dj) === 2) {
                                        if (imageData[i + di][j + dj] > 0.5) {
                                            surrounding++;
                                        }
                                    }
                                }
                            }
                            if (surrounding >= 6) loops += 0.1;
                        }
                    }
                }

                // Endpoint detection
                for (let i = 1; i < height - 1; i++) {
                    for (let j = 1; j < width - 1; j++) {
                        if (imageData[i][j] > 0.5) {
                            let neighbors = 0;
                            for (let di = -1; di <= 1; di++) {
                                for (let dj = -1; dj <= 1; dj++) {
                                    if (di !== 0 || dj !== 0) {
                                        if (imageData[i + di][j + dj] > 0.5) {
                                            neighbors++;
                                        }
                                    }
                                }
                            }
                            if (neighbors === 1) endpoints += 0.1;
                            if (neighbors >= 4) intersections += 0.1;
                        }
                    }
                }

                return { loops, openings, endpoints, intersections };
            }

            calculateAspectRatio(imageData) {
                const height = imageData.length;
                const width = imageData[0].length;

                let minRow = height, maxRow = 0, minCol = width, maxCol = 0;

                for (let i = 0; i < height; i++) {
                    for (let j = 0; j < width; j++) {
                        if (imageData[i][j] > 0.3) {
                            minRow = Math.min(minRow, i);
                            maxRow = Math.max(maxRow, i);
                            minCol = Math.min(minCol, j);
                            maxCol = Math.max(maxCol, j);
                        }
                    }
                }

                const objHeight = maxRow - minRow + 1;
                const objWidth = maxCol - minCol + 1;

                return objWidth / objHeight;
            }

            calculateVerticalSymmetry(imageData) {
                const height = imageData.length;
                const width = imageData[0].length;
                let symmetryScore = 0;
                let comparisons = 0;

                for (let i = 0; i < height; i++) {
                    for (let j = 0; j < width/2; j++) {
                        const left = imageData[i][j];
                        const right = imageData[i][width - 1 - j];
                        symmetryScore += 1 - Math.abs(left - right);
                        comparisons++;
                    }
                }

                return comparisons > 0 ? symmetryScore / comparisons : 0;
            }

            calculateHorizontalSymmetry(imageData) {
                const height = imageData.length;
                const width = imageData[0].length;
                let symmetryScore = 0;
                let comparisons = 0;

                for (let i = 0; i < height/2; i++) {
                    for (let j = 0; j < width; j++) {
                        const top = imageData[i][j];
                        const bottom = imageData[height - 1 - i][j];
                        symmetryScore += 1 - Math.abs(top - bottom);
                        comparisons++;
                    }
                }

                return comparisons > 0 ? symmetryScore / comparisons : 0;
            }

            calculateCompactness(imageData) {
                const height = imageData.length;
                const width = imageData[0].length;
                let totalPixels = 0;
                let boundingBoxArea = 0;

                let minRow = height, maxRow = 0, minCol = width, maxCol = 0;

                for (let i = 0; i < height; i++) {
                    for (let j = 0; j < width; j++) {
                        if (imageData[i][j] > 0.3) {
                            totalPixels++;
                            minRow = Math.min(minRow, i);
                            maxRow = Math.max(maxRow, i);
                            minCol = Math.min(minCol, j);
                            maxCol = Math.max(maxCol, j);
                        }
                    }
                }

                if (totalPixels > 0) {
                    boundingBoxArea = (maxRow - minRow + 1) * (maxCol - minCol + 1);
                    return totalPixels / boundingBoxArea;
                }

                return 0;
            }

            detectCircularPattern(imageData) {
                const height = imageData.length;
                const width = imageData[0].length;
                const centerX = width / 2;
                const centerY = height / 2;

                let circularScore = 0;
                let samples = 0;

                for (let radius = 3; radius < Math.min(width, height) / 3; radius++) {
                    let edgePixels = 0;
                    let totalSamples = 0;

                    for (let angle = 0; angle < 2 * Math.PI; angle += 0.2) {
                        const x = Math.round(centerX + radius * Math.cos(angle));
                        const y = Math.round(centerY + radius * Math.sin(angle));

                        if (x >= 0 && x < width && y >= 0 && y < height) {
                            if (imageData[y][x] > 0.5) edgePixels++;
                            totalSamples++;
                        }
                    }

                    if (totalSamples > 0) {
                        circularScore += edgePixels / totalSamples;
                        samples++;
                    }
                }

                return samples > 0 ? circularScore / samples : 0;
            }

            detectStraightPattern(imageData) {
                const lineFeatures = this.detectLines(imageData);
                return Math.max(lineFeatures.horizontal, lineFeatures.vertical, lineFeatures.diagonal);
            }

            detectCurvedPattern(imageData) {
                const height = imageData.length;
                const width = imageData[0].length;
                let curveScore = 0;
                let samples = 0;

                // Detect curves by looking for direction changes
                for (let i = 2; i < height - 2; i++) {
                    for (let j = 2; j < width - 2; j++) {
                        if (imageData[i][j] > 0.5) {
                            let directions = [];

                            // Check 8 directions
                            for (let di = -1; di <= 1; di++) {
                                for (let dj = -1; dj <= 1; dj++) {
                                    if (di !== 0 || dj !== 0) {
                                        if (imageData[i + di][j + dj] > 0.5) {
                                            directions.push([di, dj]);
                                        }
                                    }
                                }
                            }

                            // If we have 2 neighbors, check if they form a curve
                            if (directions.length === 2) {
                                const [d1, d2] = directions;
                                const dot = d1[0] * d2[0] + d1[1] * d2[1];
                                if (dot === 0) { // Perpendicular = curve
                                    curveScore += 0.1;
                                }
                            }
                            samples++;
                        }
                    }
                }

                return samples > 0 ? curveScore / samples : 0;
            }

            intelligentClassification(features, denseOutputs) {
                const scores = new Array(10).fill(0);

                // Digit 0: Circle with hole in middle
                scores[0] = this.scoreDigit0(features);

                // Digit 1: Vertical line, narrow
                scores[1] = this.scoreDigit1(features);

                // Digit 2: Horizontal segments, no loops
                scores[2] = this.scoreDigit2(features);

                // Digit 3: Curved right side, horizontal segments
                scores[3] = this.scoreDigit3(features);

                // Digit 4: Vertical + horizontal intersection
                scores[4] = this.scoreDigit4(features);

                // Digit 5: Top horizontal, middle horizontal, bottom horizontal
                scores[5] = this.scoreDigit5(features);

                // Digit 6: Loop at bottom, opening at top
                scores[6] = this.scoreDigit6(features);

                // Digit 7: Top horizontal + diagonal
                scores[7] = this.scoreDigit7(features);

                // Digit 8: Two loops, symmetric
                scores[8] = this.scoreDigit8(features);

                // Digit 9: Loop at top, opening at bottom
                scores[9] = this.scoreDigit9(features);

                // Apply training progress and tuning parameters
                for (let i = 0; i < scores.length; i++) {
                    scores[i] *= (1 + this.trainingProgress * this.tuningParams.trainingIntensity);
                    scores[i] *= this.tuningParams.patternSensitivity;
                    scores[i] += (Math.random() - 0.5) * (1 - this.tuningParams.noiseReduction) * 0.1;
                    scores[i] = Math.max(0, scores[i]);
                }

                // Normalize to probabilities
                const total = scores.reduce((a, b) => a + b, 0);
                if (total > 0) {
                    return scores.map(s => s / total);
                } else {
                    return new Array(10).fill(0.1);
                }
            }

            scoreDigit0(features) {
                let score = 0;

                // Strong circular pattern
                score += features.circularPattern * 3;

                // Balanced left/right density
                const leftRightBalance = 1 - Math.abs(features.leftDensity - features.rightDensity);
                score += leftRightBalance * 2;

                // High vertical symmetry
                score += features.symmetryVertical * 2;

                // Low straight pattern (should be curved)
                score += (1 - features.straightPattern) * 1;

                // Moderate compactness (not too dense)
                score += (features.compactness > 0.3 && features.compactness < 0.7) ? 1 : 0;

                // Penalize too many horizontal lines
                score -= features.horizontalLines * 0.5;

                return score;
            }

            scoreDigit1(features) {
                let score = 0;

                // Strong vertical lines
                score += features.verticalLines * 4;

                // High straight pattern
                score += features.straightPattern * 3;

                // Low horizontal lines
                score += (1 - features.horizontalLines) * 2;

                // Narrow aspect ratio (tall and thin)
                score += (features.aspectRatio < 0.6) ? 2 : 0;

                // Center density should be high
                score += features.centerDensity * 2;

                // Low circular pattern
                score += (1 - features.circularPattern) * 1;

                // Few endpoints (should be mostly one line)
                score += (features.endpoints < 0.5) ? 1 : 0;

                return score;
            }

            scoreDigit2(features) {
                let score = 0;

                // Strong horizontal lines
                score += features.horizontalLines * 3;

                // High top and bottom density
                score += (features.topDensity + features.bottomDensity) * 1.5;

                // Moderate middle density
                score += (features.middleDensity > 0.3 && features.middleDensity < 0.8) ? 1 : 0;

                // Low circular pattern
                score += (1 - features.circularPattern) * 2;

                // Some diagonal elements
                score += features.diagonalLines * 0.5;

                // Multiple endpoints
                score += (features.endpoints > 0.3) ? 1 : 0;

                // Low vertical symmetry
                score += (1 - features.symmetryVertical) * 0.5;

                return score;
            }

            scoreDigit3(features) {
                let score = 0;

                // Curved pattern
                score += features.curvedPattern * 3;

                // Strong horizontal lines
                score += features.horizontalLines * 2;

                // Right-heavy density
                score += (features.rightDensity > features.leftDensity) ? 2 : 0;

                // High top and bottom density
                score += (features.topDensity + features.bottomDensity) * 1.5;

                // Low vertical symmetry
                score += (1 - features.symmetryVertical) * 1;

                // Some loops but not too many
                score += (features.loops > 0.1 && features.loops < 0.5) ? 1 : 0;

                return score;
            }

            scoreDigit4(features) {
                let score = 0;

                // Strong vertical and horizontal lines
                score += (features.verticalLines + features.horizontalLines) * 2;

                // High intersections
                score += features.intersections * 3;

                // Left and center density
                score += (features.leftDensity + features.centerDensity) * 1.5;

                // Low bottom density (open bottom)
                score += (1 - features.bottomDensity) * 1;

                // Low circular pattern
                score += (1 - features.circularPattern) * 2;

                // High straight pattern
                score += features.straightPattern * 2;

                return score;
            }

            scoreDigit5(features) {
                let score = 0;

                // Strong horizontal lines
                score += features.horizontalLines * 3;

                // High top, middle, and bottom density
                score += (features.topDensity + features.middleDensity + features.bottomDensity) * 1;

                // Left-heavy in top, right-heavy in bottom
                const topLeftHeavy = features.topDensity > 0.3 && features.leftDensity > features.rightDensity;
                const bottomRightHeavy = features.bottomDensity > 0.3 && features.rightDensity > 0.3;
                score += (topLeftHeavy && bottomRightHeavy) ? 2 : 0;

                // Some curved elements
                score += features.curvedPattern * 1;

                // Low vertical symmetry
                score += (1 - features.symmetryVertical) * 1;

                return score;
            }

            scoreDigit6(features) {
                let score = 0;

                // Strong circular pattern (but not complete circle)
                score += features.circularPattern * 2;

                // High bottom density, moderate top
                score += features.bottomDensity * 2;
                score += (features.topDensity > 0.2 && features.topDensity < 0.6) ? 1 : 0;

                // Some loops
                score += features.loops * 2;

                // Curved pattern
                score += features.curvedPattern * 2;

                // Left-heavy density
                score += (features.leftDensity > features.rightDensity) ? 1 : 0;

                // Low horizontal symmetry
                score += (1 - features.symmetryHorizontal) * 1;

                return score;
            }

            scoreDigit7(features) {
                let score = 0;

                // Strong diagonal lines
                score += features.diagonalLines * 4;

                // High top density
                score += features.topDensity * 3;

                // Strong horizontal lines at top
                score += features.horizontalLines * 2;

                // Low bottom density
                score += (1 - features.bottomDensity) * 1;

                // Right-heavy density
                score += (features.rightDensity > features.leftDensity) ? 2 : 0;

                // Low circular pattern
                score += (1 - features.circularPattern) * 2;

                // High straight pattern
                score += features.straightPattern * 1;

                return score;
            }

            scoreDigit8(features) {
                let score = 0;

                // Strong circular pattern
                score += features.circularPattern * 3;

                // High loops
                score += features.loops * 4;

                // High vertical symmetry
                score += features.symmetryVertical * 3;

                // High horizontal symmetry
                score += features.symmetryHorizontal * 2;

                // Balanced density in all regions
                const balanced = 1 - Math.abs(features.topDensity - features.bottomDensity);
                score += balanced * 2;

                // High compactness
                score += features.compactness * 2;

                // Some intersections
                score += features.intersections * 1;

                return score;
            }

            scoreDigit9(features) {
                let score = 0;

                // Strong circular pattern
                score += features.circularPattern * 2;

                // High top density
                score += features.topDensity * 2;

                // Some loops
                score += features.loops * 2;

                // Curved pattern
                score += features.curvedPattern * 2;

                // Right-heavy density
                score += (features.rightDensity > features.leftDensity) ? 2 : 0;

                // High vertical lines (for the tail)
                score += features.verticalLines * 1;

                // Low horizontal symmetry
                score += (1 - features.symmetryHorizontal) * 1;

                return score;
            }

            updateTuningParameters() {
                this.tuningParams.learningRate = parseFloat(document.getElementById('learningRate').value);
                this.tuningParams.patternSensitivity = parseFloat(document.getElementById('patternSensitivity').value);
                this.tuningParams.featureStrength = parseFloat(document.getElementById('featureStrength').value);
                this.tuningParams.noiseReduction = parseFloat(document.getElementById('noiseReduction').value);
                this.tuningParams.trainingIntensity = parseFloat(document.getElementById('trainingIntensity').value);
            }

            simulateConvolution(input, features) {
                const outputs = [];
                this.calculations.convolution = [];

                for (let f = 0; f < this.config.convFilters; f++) {
                    // Simulate convolution for each filter
                    const featureMap = [];
                    const filterCalc = {
                        filterIndex: f,
                        inputSize: [28, 28],
                        filterSize: this.config.filterSize,
                        outputSize: [26, 26], // 28-3+1
                        sampleCalculations: []
                    };

                    // Sample calculation for position (5,5)
                    let convSum = 0;
                    const sampleInputs = [];
                    const sampleWeights = [];

                    for (let i = 0; i < this.config.filterSize; i++) {
                        for (let j = 0; j < this.config.filterSize; j++) {
                            const inputVal = input[5 + i] ? input[5 + i][5 + j] || 0 : 0;
                            const weight = (Math.sin(f + i + j) + 1) / 2; // Simulated weight
                            sampleInputs.push(inputVal);
                            sampleWeights.push(weight);
                            convSum += inputVal * weight;
                        }
                    }

                    const activation = Math.max(0, convSum); // ReLU

                    filterCalc.sampleCalculations.push({
                        position: [5, 5],
                        inputs: sampleInputs,
                        weights: sampleWeights,
                        convSum: convSum,
                        activation: activation
                    });

                    // Generate feature map values
                    for (let i = 0; i < 26; i++) {
                        featureMap[i] = [];
                        for (let j = 0; j < 26; j++) {
                            featureMap[i][j] = Math.random() * 0.5 + activation * 0.3;
                        }
                    }

                    outputs.push(featureMap);
                    this.calculations.convolution.push(filterCalc);
                }

                return outputs;
            }

            simulatePooling(convOutputs) {
                const outputs = [];
                this.calculations.pooling = [];

                for (let f = 0; f < convOutputs.length; f++) {
                    const pooled = [];
                    const poolCalc = {
                        filterIndex: f,
                        inputSize: [26, 26],
                        poolSize: this.config.poolSize,
                        outputSize: [13, 13], // 26/2
                        sampleCalculations: []
                    };

                    // Sample calculation for position (2,2) -> pool region (4,4) to (5,5)
                    const poolRegion = [];
                    for (let i = 0; i < this.config.poolSize; i++) {
                        for (let j = 0; j < this.config.poolSize; j++) {
                            const val = convOutputs[f][4 + i] ? convOutputs[f][4 + i][4 + j] || 0 : 0;
                            poolRegion.push(val);
                        }
                    }
                    const maxVal = Math.max(...poolRegion);

                    poolCalc.sampleCalculations.push({
                        outputPosition: [2, 2],
                        inputRegion: [[4, 4], [5, 5]],
                        values: poolRegion,
                        maxValue: maxVal
                    });

                    // Generate pooled values
                    for (let i = 0; i < 13; i++) {
                        pooled[i] = [];
                        for (let j = 0; j < 13; j++) {
                            pooled[i][j] = Math.random() * 0.4 + maxVal * 0.3;
                        }
                    }

                    outputs.push(pooled);
                    this.calculations.pooling.push(poolCalc);
                }

                return outputs;
            }

            simulateFlatten(poolOutputs) {
                const flattened = [];
                for (let f = 0; f < poolOutputs.length; f++) {
                    for (let i = 0; i < 13; i++) {
                        for (let j = 0; j < 13; j++) {
                            flattened.push(poolOutputs[f][i][j]);
                        }
                    }
                }
                return flattened; // 8 * 13 * 13 = 1352 values
            }

            simulateDense(flattened) {
                const outputs = [];
                this.calculations.dense = [];

                for (let i = 0; i < this.config.denseUnits; i++) {
                    let sum = 0;
                    const sampleWeights = [];
                    const sampleInputs = [];

                    // Sample first 9 connections for tooltip
                    for (let j = 0; j < Math.min(9, flattened.length); j++) {
                        const weight = (Math.sin(i + j) + 1) / 2; // Simulated weight
                        sampleWeights.push(weight);
                        sampleInputs.push(flattened[j]);
                        sum += flattened[j] * weight;
                    }

                    const bias = (Math.cos(i) + 1) / 4; // Simulated bias
                    const preActivation = sum + bias;
                    const activation = Math.max(0, preActivation); // ReLU

                    outputs.push(activation);

                    if (i < 8) { // Store calculations for first 8 neurons for tooltips
                        this.calculations.dense.push({
                            neuronIndex: i,
                            sampleInputs: sampleInputs,
                            sampleWeights: sampleWeights,
                            weightedSum: sum,
                            bias: bias,
                            preActivation: preActivation,
                            activation: activation,
                            totalConnections: flattened.length
                        });
                    }
                }

                return outputs;
            }

            simulateOutput(denseOutputs) {
                const logits = [];
                this.calculations.softmax = [];

                // Generate logits
                for (let i = 0; i < this.config.outputClasses; i++) {
                    let sum = 0;
                    const sampleWeights = [];
                    const sampleInputs = [];

                    // Sample connections for tooltip
                    for (let j = 0; j < Math.min(8, denseOutputs.length); j++) {
                        const weight = (Math.sin(i + j + 10) + 1) / 2;
                        sampleWeights.push(weight);
                        sampleInputs.push(denseOutputs[j]);
                        sum += denseOutputs[j] * weight;
                    }

                    const bias = (Math.cos(i + 5) + 1) / 4;
                    const logit = sum + bias;
                    logits.push(logit);

                    this.calculations.softmax.push({
                        classIndex: i,
                        sampleInputs: sampleInputs,
                        sampleWeights: sampleWeights,
                        weightedSum: sum,
                        bias: bias,
                        logit: logit
                    });
                }

                // Apply softmax
                const maxLogit = Math.max(...logits);
                const expLogits = logits.map(x => Math.exp(x - maxLogit));
                const sumExp = expLogits.reduce((a, b) => a + b, 0);
                const probabilities = expLogits.map(x => x / sumExp);

                // Update softmax calculations with final probabilities
                for (let i = 0; i < this.calculations.softmax.length; i++) {
                    this.calculations.softmax[i].expLogit = expLogits[i];
                    this.calculations.softmax[i].probability = probabilities[i];
                    this.calculations.softmax[i].sumExp = sumExp;
                }

                return probabilities;
            }
            
            simulateTraining() {
                this.epoch++;

                // Update tuning parameters from UI
                this.updateTuningParameters();

                // Training progress affects learning
                const learningRate = this.tuningParams.learningRate;
                const trainingIntensity = this.tuningParams.trainingIntensity;

                // Progress calculation with learning rate effect
                const progressIncrement = learningRate * trainingIntensity * 0.02;
                this.trainingProgress = Math.min(1, this.trainingProgress + progressIncrement);

                // Loss calculation (decreases with training and better parameters)
                const baseLoss = 2.303;
                const learningEffect = this.trainingProgress * (1 + trainingIntensity);
                const parameterEffect = (this.tuningParams.patternSensitivity + this.tuningParams.featureStrength) / 4;
                const noiseEffect = this.tuningParams.noiseReduction * 0.5;

                const loss = baseLoss * Math.exp(-learningEffect * 2) * (1 - parameterEffect) * (1 - noiseEffect) +
                           0.05 * Math.random();

                // Accuracy calculation (improves with training and tuning)
                const baseAccuracy = 10;
                const maxAccuracy = 95;
                const trainingBonus = this.trainingProgress * 60 * trainingIntensity;
                const tuningBonus = (this.tuningParams.patternSensitivity +
                                   this.tuningParams.featureStrength +
                                   this.tuningParams.noiseReduction) * 8;
                const learningBonus = Math.log(1 + learningRate * 100) * 5;

                const accuracy = baseAccuracy + trainingBonus + tuningBonus + learningBonus +
                               (Math.random() - 0.5) * 3;

                // Validation accuracy (slightly different from training)
                const valAccuracy = accuracy * (0.9 + Math.random() * 0.15);

                this.lossHistory.push(loss);

                return {
                    epoch: this.epoch,
                    loss: Math.max(0.01, loss),
                    accuracy: Math.min(maxAccuracy, Math.max(baseAccuracy, accuracy)),
                    valAccuracy: Math.min(maxAccuracy, Math.max(baseAccuracy, valAccuracy))
                };
            }
        }
        
        // CNN App
        class CNNApp {
            constructor() {
                this.network = new SimpleCNN();
                this.isDrawing = false;
                this.trainingInterval = null;
                
                this.initializeApp();
            }
            
            initializeApp() {
                console.log('✅ Initializing CNN App...');
                
                this.setupCanvas();
                this.setupEventListeners();
                this.generateSampleDigits();
                this.loadSampleImage('digit0');
                this.drawNetwork();
                
                console.log('🎉 CNN App ready!');
            }
            
            setupCanvas() {
                this.canvas = document.getElementById('cnnCanvas');
                this.ctx = this.canvas.getContext('2d');

                this.drawingCanvas = document.getElementById('drawingCanvas');
                this.drawingCtx = this.drawingCanvas.getContext('2d');

                // Tooltip elements
                this.tooltip = document.getElementById('cnnTooltip');
                this.tooltipTitle = this.tooltip.querySelector('.tooltip-title');
                this.tooltipContent = this.tooltip.querySelector('.tooltip-content');

                // Setup canvas mouse events for tooltips
                this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
                this.canvas.addEventListener('mouseleave', () => this.hideTooltip());

                this.clearDrawingCanvas();
                console.log('✅ Canvas setup complete');
            }
            
            setupEventListeners() {
                // Training controls
                document.getElementById('startCNNTraining').onclick = () => this.startTraining();
                document.getElementById('forwardPassCNN').onclick = () => this.demonstrateForwardPass();
                document.getElementById('resetCNN').onclick = () => this.resetNetwork();
                document.getElementById('pauseResumeCNN').onclick = () => this.togglePause();
                
                // Image controls
                document.getElementById('imageSelect').onchange = (e) => this.loadSampleImage(e.target.value);
                document.getElementById('classifyImage').onclick = () => this.classifyCurrentImage();
                document.getElementById('clearDrawing').onclick = () => this.clearDrawingCanvas();
                
                // Drawing events
                this.drawingCanvas.onmousedown = (e) => { this.isDrawing = true; this.draw(e); };
                this.drawingCanvas.onmousemove = (e) => { if (this.isDrawing) this.draw(e); };
                this.drawingCanvas.onmouseup = () => { this.isDrawing = false; };
                this.drawingCanvas.onmouseout = () => { this.isDrawing = false; };

                // Fine-tuning parameter controls
                this.setupTuningControls();

                console.log('✅ Event listeners setup complete');
            }

            setupTuningControls() {
                const controls = [
                    { id: 'learningRate', display: 'lrValue' },
                    { id: 'patternSensitivity', display: 'patternValue' },
                    { id: 'featureStrength', display: 'featureValue' },
                    { id: 'noiseReduction', display: 'noiseValue' },
                    { id: 'trainingIntensity', display: 'intensityValue' }
                ];

                controls.forEach(control => {
                    const slider = document.getElementById(control.id);
                    const display = document.getElementById(control.display);

                    if (slider && display) {
                        slider.oninput = () => {
                            display.textContent = slider.value;
                            this.network.updateTuningParameters();
                            // Re-classify current image with new parameters
                            this.classifyCurrentImage();
                        };
                    }
                });

                console.log('✅ Tuning controls setup complete');
            }
            
            generateSampleDigits() {
                this.sampleDigits = {};
                
                for (let digit = 0; digit < 10; digit++) {
                    const pattern = [];
                    for (let i = 0; i < 28; i++) {
                        pattern[i] = [];
                        for (let j = 0; j < 28; j++) {
                            pattern[i][j] = this.generateDigitPixel(digit, i, j);
                        }
                    }
                    this.sampleDigits[`digit${digit}`] = pattern;
                }
                
                console.log('✅ Sample digits generated');
            }
            
            generateDigitPixel(digit, row, col) {
                const centerX = 14, centerY = 14;
                const distFromCenter = Math.sqrt((row - centerY) ** 2 + (col - centerX) ** 2);
                const noise = Math.random() * 0.1;

                switch (digit) {
                    case 0: // Circle
                        return (distFromCenter > 6 && distFromCenter < 10) ? 0.8 + noise : noise;

                    case 1: // Vertical line
                        return (col > 12 && col < 16 && row > 4 && row < 24) ? 0.9 + noise : noise;

                    case 2: // Horizontal segments
                        const is2Pattern = (row < 8 && col > 6 && col < 22) || // top
                                         (row > 20 && col > 6 && col < 22) || // bottom
                                         (row > 12 && row < 16 && col > 6 && col < 22); // middle
                        return is2Pattern ? 0.8 + noise : noise;

                    case 3: // Right curves
                        const is3Pattern = (row < 8 && col > 6 && col < 20) || // top
                                         (row > 20 && col > 6 && col < 20) || // bottom
                                         (row > 12 && row < 16 && col > 6 && col < 18) || // middle
                                         (col > 18 && col < 22 && row > 8 && row < 20); // right edge
                        return is3Pattern ? 0.8 + noise : noise;

                    case 4: // Vertical + horizontal
                        const is4Pattern = (col > 12 && col < 16) || // vertical line
                                         (row > 12 && row < 16 && col > 6 && col < 20) || // horizontal
                                         (col > 6 && col < 10 && row > 4 && row < 16); // left vertical
                        return is4Pattern ? 0.8 + noise : noise;

                    case 5: // S-like pattern
                        const is5Pattern = (row < 8 && col > 6 && col < 22) || // top
                                         (row > 20 && col > 6 && col < 22) || // bottom
                                         (row > 12 && row < 16 && col > 6 && col < 18) || // middle
                                         (col > 6 && col < 10 && row > 4 && row < 16) || // left top
                                         (col > 18 && col < 22 && row > 16 && row < 24); // right bottom
                        return is5Pattern ? 0.8 + noise : noise;

                    case 6: // Loop with opening
                        const is6Pattern = (distFromCenter > 6 && distFromCenter < 10 && col < 16) || // left arc
                                         (row > 12 && row < 16 && col > 6 && col < 18); // middle line
                        return is6Pattern ? 0.8 + noise : noise;

                    case 7: // Diagonal
                        const is7Pattern = (row < 8 && col > 6 && col < 22) || // top
                                         (Math.abs((row - 4) * 0.8 - (col - 20)) < 2 && row > 8); // diagonal
                        return is7Pattern ? 0.8 + noise : noise;

                    case 8: // Double loop
                        const is8Pattern = (distFromCenter > 4 && distFromCenter < 8) || // outer circle
                                         (row > 12 && row < 16 && col > 8 && col < 20); // middle line
                        return is8Pattern ? 0.8 + noise : noise;

                    case 9: // Loop with tail
                        const is9Pattern = (distFromCenter > 6 && distFromCenter < 10 && col > 12) || // right arc
                                         (row > 12 && row < 16 && col > 10 && col < 22) || // middle line
                                         (col > 18 && col < 22 && row > 16); // right tail
                        return is9Pattern ? 0.8 + noise : noise;

                    default:
                        return noise;
                }
            }

            loadSampleImage(imageKey) {
                if (this.sampleDigits[imageKey]) {
                    this.drawImageOnCanvas(this.sampleDigits[imageKey]);
                    console.log(`📷 Loaded sample image: ${imageKey}`);
                }
            }

            drawImageOnCanvas(imageData) {
                const canvas = this.drawingCanvas;
                const ctx = this.drawingCtx;

                ctx.fillStyle = '#fff';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                const cellWidth = canvas.width / 28;
                const cellHeight = canvas.height / 28;

                for (let i = 0; i < 28; i++) {
                    for (let j = 0; j < 28; j++) {
                        const intensity = imageData[i][j];
                        const gray = Math.floor((1 - intensity) * 255);
                        ctx.fillStyle = `rgb(${gray}, ${gray}, ${gray})`;
                        ctx.fillRect(j * cellWidth, i * cellHeight, cellWidth, cellHeight);
                    }
                }

                ctx.strokeStyle = '#ddd';
                ctx.lineWidth = 2;
                ctx.strokeRect(0, 0, canvas.width, canvas.height);

                this.classifyCurrentImage();
            }

            draw(e) {
                const rect = this.drawingCanvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                this.drawingCtx.fillStyle = '#000';
                this.drawingCtx.beginPath();
                this.drawingCtx.arc(x, y, 8, 0, 2 * Math.PI);
                this.drawingCtx.fill();

                clearTimeout(this.drawingTimeout);
                this.drawingTimeout = setTimeout(() => {
                    this.classifyCurrentImage();
                }, 300);
            }

            clearDrawingCanvas() {
                this.drawingCtx.fillStyle = '#fff';
                this.drawingCtx.fillRect(0, 0, this.drawingCanvas.width, this.drawingCanvas.height);

                this.drawingCtx.strokeStyle = '#ddd';
                this.drawingCtx.lineWidth = 2;
                this.drawingCtx.strokeRect(0, 0, this.drawingCanvas.width, this.drawingCanvas.height);

                this.displayClassificationResult({
                    class: 0,
                    confidence: 0,
                    probabilities: new Array(10).fill(0.1)
                });

                console.log('🧹 Canvas cleared');
            }

            getImageFromCanvas() {
                const canvas = this.drawingCanvas;
                const ctx = this.drawingCtx;
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                const data = imageData.data;

                const result = [];
                const scaleX = canvas.width / 28;
                const scaleY = canvas.height / 28;

                for (let i = 0; i < 28; i++) {
                    result[i] = [];
                    for (let j = 0; j < 28; j++) {
                        const x = Math.floor(j * scaleX);
                        const y = Math.floor(i * scaleY);
                        const index = (y * canvas.width + x) * 4;
                        const gray = (data[index] + data[index + 1] + data[index + 2]) / 3;
                        result[i][j] = 1 - (gray / 255);
                    }
                }

                return result;
            }

            classifyCurrentImage() {
                const imageData = this.getImageFromCanvas();
                const prediction = this.network.predict(imageData);
                this.displayClassificationResult(prediction);

                console.log(`🔍 Classification: ${prediction.class} (${prediction.confidence.toFixed(1)}%)`);
            }

            displayClassificationResult(prediction) {
                document.getElementById('predictedDigit').textContent = prediction.class;
                document.getElementById('confidenceScore').textContent = prediction.confidence.toFixed(1) + '%';
                this.updateProbabilityBars(prediction.probabilities);
            }

            updateProbabilityBars(probabilities) {
                const container = document.getElementById('probabilityBars');
                container.innerHTML = '';

                for (let i = 0; i < probabilities.length; i++) {
                    const probability = probabilities[i] * 100;

                    const barDiv = document.createElement('div');
                    barDiv.className = 'prob-bar';

                    barDiv.innerHTML = `
                        <span class="prob-label">${i}</span>
                        <div class="prob-fill">
                            <div class="prob-value" style="width: ${probability}%"></div>
                        </div>
                        <span class="prob-percent">${probability.toFixed(1)}%</span>
                    `;

                    container.appendChild(barDiv);
                }
            }

            drawNetwork() {
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

                // Draw simple network representation
                const layers = [
                    { x: 100, y: 150, width: 80, height: 100, label: 'Input\n28×28', color: '#4CAF50' },
                    { x: 250, y: 120, width: 100, height: 160, label: 'Conv\n8 Filters', color: '#2196F3' },
                    { x: 420, y: 150, width: 80, height: 100, label: 'Pool\n2×2', color: '#FF9800' },
                    { x: 570, y: 170, width: 60, height: 60, label: 'Dense\n64', color: '#F44336' },
                    { x: 700, y: 175, width: 60, height: 50, label: 'Output\n10', color: '#9C27B0' }
                ];

                // Draw connections
                this.ctx.strokeStyle = '#ddd';
                this.ctx.lineWidth = 2;
                for (let i = 0; i < layers.length - 1; i++) {
                    const from = layers[i];
                    const to = layers[i + 1];
                    this.ctx.beginPath();
                    this.ctx.moveTo(from.x + from.width, from.y + from.height / 2);
                    this.ctx.lineTo(to.x, to.y + to.height / 2);
                    this.ctx.stroke();
                }

                // Draw layers
                for (const layer of layers) {
                    this.ctx.fillStyle = layer.color + '40';
                    this.ctx.fillRect(layer.x, layer.y, layer.width, layer.height);

                    this.ctx.strokeStyle = layer.color;
                    this.ctx.lineWidth = 2;
                    this.ctx.strokeRect(layer.x, layer.y, layer.width, layer.height);

                    this.ctx.fillStyle = '#333';
                    this.ctx.font = 'bold 12px Arial';
                    this.ctx.textAlign = 'center';
                    const lines = layer.label.split('\n');
                    for (let i = 0; i < lines.length; i++) {
                        this.ctx.fillText(
                            lines[i],
                            layer.x + layer.width / 2,
                            layer.y - 10 + i * 14
                        );
                    }
                }

                console.log('✅ Network visualization drawn');
            }

            handleMouseMove(e) {
                const rect = this.canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                // Check which layer is being hovered
                const hoveredLayer = this.getLayerAt(x, y);
                if (hoveredLayer) {
                    this.showLayerTooltip(hoveredLayer, e.clientX, e.clientY);
                } else {
                    this.hideTooltip();
                }
            }

            getLayerAt(x, y) {
                const layers = [
                    { type: 'input', x: 100, y: 150, width: 80, height: 100, index: 0 },
                    { type: 'conv', x: 250, y: 120, width: 100, height: 160, index: 1 },
                    { type: 'pool', x: 420, y: 150, width: 80, height: 100, index: 2 },
                    { type: 'dense', x: 570, y: 170, width: 60, height: 60, index: 3 },
                    { type: 'output', x: 700, y: 175, width: 60, height: 50, index: 4 }
                ];

                for (const layer of layers) {
                    if (x >= layer.x && x <= layer.x + layer.width &&
                        y >= layer.y && y <= layer.y + layer.height) {
                        return layer;
                    }
                }
                return null;
            }

            showLayerTooltip(layer, clientX, clientY) {
                const layerTitles = {
                    'input': 'Input Layer',
                    'conv': 'Convolutional Layer',
                    'pool': 'Max Pooling Layer',
                    'dense': 'Dense Layer',
                    'output': 'Output Layer'
                };

                this.tooltipTitle.textContent = layerTitles[layer.type];

                let content = this.getLayerTooltipContent(layer.type);
                this.tooltipContent.innerHTML = content;

                this.showTooltip(clientX, clientY);
            }

            getLayerTooltipContent(layerType) {
                switch (layerType) {
                    case 'input':
                        return this.getInputTooltip();
                    case 'conv':
                        return this.getConvTooltip();
                    case 'pool':
                        return this.getPoolTooltip();
                    case 'dense':
                        return this.getDenseTooltip();
                    case 'output':
                        return this.getOutputTooltip();
                    default:
                        return '<div>Layer information not available</div>';
                }
            }

            getInputTooltip() {
                let content = '<div><strong>Function:</strong> Receives input image data</div>';
                content += '<div><strong>Shape:</strong> 28×28 pixels</div>';
                content += '<div><strong>Values:</strong> Pixel intensities (0-1)</div>';

                if (this.network.layerOutputs.input) {
                    const input = this.network.layerOutputs.input;
                    const samplePixel = input[14] ? input[14][14] || 0 : 0;

                    content += '<div style="margin-top: 8px; border-top: 1px solid #444; padding-top: 8px;"><strong>Sample Calculation:</strong></div>';
                    content += '<div class="math-step">';
                    content += '<div class="step-title">Pixel Normalization</div>';
                    content += '<div class="math-formula">normalized = pixel_value / 255</div>';
                    content += '<div>Center pixel (14,14): <span class="tooltip-value">' + samplePixel.toFixed(4) + '</span></div>';
                    content += '</div>';
                }

                return content;
            }

            getConvTooltip() {
                let content = '<div><strong>Function:</strong> Feature detection using filters</div>';
                content += '<div><strong>Filters:</strong> 8 different feature detectors</div>';
                content += '<div><strong>Filter Size:</strong> 3×3 kernels</div>';
                content += '<div><strong>Output:</strong> 26×26 feature maps</div>';

                if (this.network.calculations.convolution.length > 0) {
                    const calc = this.network.calculations.convolution[0];
                    const sample = calc.sampleCalculations[0];

                    content += '<div style="margin-top: 8px; border-top: 1px solid #444; padding-top: 8px;"><strong>Mathematical Process:</strong></div>';
                    content += '<div class="math-step">';
                    content += '<div class="step-title">Step 1: Convolution Operation</div>';
                    content += '<div class="math-formula">output[i,j] = Σ(filter[m,n] × input[i+m,j+n])</div>';
                    content += '<div>Sample position (5,5):</div>';

                    for (let i = 0; i < 3; i++) {
                        const idx = i * 3;
                        content += '<div>';
                        for (let j = 0; j < 3; j++) {
                            const weight = sample.weights[idx + j];
                            const input = sample.inputs[idx + j];
                            const product = weight * input;
                            content += `<span class="tooltip-value">${weight.toFixed(2)}</span>×<span class="tooltip-value">${input.toFixed(2)}</span> `;
                        }
                        content += '</div>';
                    }

                    content += '<div class="step-result">Convolution Sum = <span class="tooltip-value">' + sample.convSum.toFixed(4) + '</span></div>';
                    content += '</div>';

                    content += '<div class="math-step">';
                    content += '<div class="step-title">Step 2: ReLU Activation</div>';
                    content += '<div class="math-formula">activation = max(0, convolution_sum)</div>';
                    content += '<div class="step-result">Final Output = <span class="tooltip-value">' + sample.activation.toFixed(4) + '</span></div>';
                    content += '</div>';
                }

                return content;
            }

            getPoolTooltip() {
                let content = '<div><strong>Function:</strong> Spatial dimension reduction</div>';
                content += '<div><strong>Pool Size:</strong> 2×2 windows</div>';
                content += '<div><strong>Operation:</strong> Maximum value selection</div>';
                content += '<div><strong>Output:</strong> 13×13 feature maps</div>';

                if (this.network.calculations.pooling.length > 0) {
                    const calc = this.network.calculations.pooling[0];
                    const sample = calc.sampleCalculations[0];

                    content += '<div style="margin-top: 8px; border-top: 1px solid #444; padding-top: 8px;"><strong>Mathematical Process:</strong></div>';
                    content += '<div class="math-step">';
                    content += '<div class="step-title">Max Pooling Operation</div>';
                    content += '<div class="math-formula">output = max(pool_region)</div>';
                    content += '<div>Sample region (4,4) to (5,5):</div>';
                    content += '<div>Values: [';
                    for (let i = 0; i < sample.values.length; i++) {
                        content += '<span class="tooltip-value">' + sample.values[i].toFixed(3) + '</span>';
                        if (i < sample.values.length - 1) content += ', ';
                    }
                    content += ']</div>';
                    content += '<div class="step-result">Maximum = <span class="tooltip-value">' + sample.maxValue.toFixed(4) + '</span></div>';
                    content += '</div>';
                }

                return content;
            }

            getDenseTooltip() {
                let content = '<div><strong>Function:</strong> High-level feature learning</div>';
                content += '<div><strong>Units:</strong> 64 fully connected neurons</div>';
                content += '<div><strong>Input:</strong> 1352 flattened features</div>';
                content += '<div><strong>Activation:</strong> ReLU function</div>';

                if (this.network.calculations.dense.length > 0) {
                    const calc = this.network.calculations.dense[0];

                    content += '<div style="margin-top: 8px; border-top: 1px solid #444; padding-top: 8px;"><strong>Mathematical Process:</strong></div>';
                    content += '<div class="math-step">';
                    content += '<div class="step-title">Step 1: Weighted Sum (Sample)</div>';
                    content += '<div class="math-formula">z = Σ(w[i] × input[i]) + bias</div>';
                    content += '<div>Sample connections (first 9 of ' + calc.totalConnections + '):</div>';

                    for (let i = 0; i < Math.min(3, calc.sampleInputs.length); i++) {
                        const weight = calc.sampleWeights[i];
                        const input = calc.sampleInputs[i];
                        const product = weight * input;
                        content += `<div><span class="tooltip-value">${weight.toFixed(3)}</span> × <span class="tooltip-value">${input.toFixed(3)}</span> = <span class="tooltip-value">${product.toFixed(3)}</span></div>`;
                    }
                    content += '<div>... (+ ' + (calc.totalConnections - 3) + ' more connections)</div>';

                    content += '<div class="step-result">Weighted Sum = <span class="tooltip-value">' + calc.weightedSum.toFixed(4) + '</span></div>';
                    content += '</div>';

                    content += '<div class="math-step">';
                    content += '<div class="step-title">Step 2: Add Bias</div>';
                    content += '<div class="math-formula">pre_activation = weighted_sum + bias</div>';
                    content += '<div>' + calc.weightedSum.toFixed(4) + ' + ' + calc.bias.toFixed(4) + ' = ' + calc.preActivation.toFixed(4) + '</div>';
                    content += '</div>';

                    content += '<div class="math-step">';
                    content += '<div class="step-title">Step 3: ReLU Activation</div>';
                    content += '<div class="math-formula">activation = max(0, pre_activation)</div>';
                    content += '<div class="step-result">Final Output = <span class="tooltip-value">' + calc.activation.toFixed(4) + '</span></div>';
                    content += '</div>';
                }

                return content;
            }

            getOutputTooltip() {
                let content = '<div><strong>Function:</strong> Classification decision</div>';
                content += '<div><strong>Classes:</strong> 10 digit categories (0-9)</div>';
                content += '<div><strong>Input:</strong> 64 dense features</div>';
                content += '<div><strong>Activation:</strong> Softmax function</div>';

                if (this.network.calculations.softmax.length > 0) {
                    const calc = this.network.calculations.softmax[0];

                    content += '<div style="margin-top: 8px; border-top: 1px solid #444; padding-top: 8px;"><strong>Mathematical Process:</strong></div>';
                    content += '<div class="math-step">';
                    content += '<div class="step-title">Step 1: Linear Transformation</div>';
                    content += '<div class="math-formula">logit = Σ(w[i] × dense[i]) + bias</div>';
                    content += '<div>Sample connections for class 0:</div>';

                    for (let i = 0; i < Math.min(3, calc.sampleInputs.length); i++) {
                        const weight = calc.sampleWeights[i];
                        const input = calc.sampleInputs[i];
                        const product = weight * input;
                        content += `<div><span class="tooltip-value">${weight.toFixed(3)}</span> × <span class="tooltip-value">${input.toFixed(3)}</span> = <span class="tooltip-value">${product.toFixed(3)}</span></div>`;
                    }

                    content += '<div class="step-result">Logit = <span class="tooltip-value">' + calc.logit.toFixed(4) + '</span></div>';
                    content += '</div>';

                    content += '<div class="math-step">';
                    content += '<div class="step-title">Step 2: Softmax Normalization</div>';
                    content += '<div class="math-formula">P(class) = e^(logit) / Σ(e^(all_logits))</div>';
                    content += '<div>e^(' + calc.logit.toFixed(4) + ') = ' + calc.expLogit.toFixed(4) + '</div>';
                    content += '<div>Sum of all exponentials = ' + calc.sumExp.toFixed(4) + '</div>';
                    content += '<div class="step-result">Probability = <span class="tooltip-value">' + (calc.probability * 100).toFixed(2) + '%</span></div>';
                    content += '</div>';
                }

                return content;
            }

            showTooltip(clientX, clientY) {
                this.tooltip.style.left = (clientX + 10) + 'px';
                this.tooltip.style.top = (clientY - 10) + 'px';
                this.tooltip.classList.add('visible');
            }

            hideTooltip() {
                this.tooltip.classList.remove('visible');
            }

            startTraining() {
                if (this.network.isTraining) {
                    this.stopTraining();
                    return;
                }

                this.network.isTraining = true;
                document.getElementById('startCNNTraining').textContent = 'Stop Training';

                this.trainStep();
                console.log('🎯 Training started');
            }

            trainStep() {
                if (!this.network.isTraining) return;

                const result = this.network.simulateTraining();

                document.getElementById('cnnEpochCount').textContent = result.epoch;
                document.getElementById('cnnLossValue').textContent = result.loss.toFixed(3);
                document.getElementById('cnnAccuracyValue').textContent = result.accuracy.toFixed(0) + '%';
                document.getElementById('valAccuracyValue').textContent = result.valAccuracy.toFixed(0) + '%';

                // Re-classify current image to show improved accuracy
                this.classifyCurrentImage();

                if (result.epoch >= 100) {
                    this.stopTraining();
                    return;
                }

                this.trainingInterval = setTimeout(() => {
                    this.trainStep();
                }, 100);
            }

            stopTraining() {
                this.network.isTraining = false;
                document.getElementById('startCNNTraining').textContent = 'Start Training';

                if (this.trainingInterval) {
                    clearTimeout(this.trainingInterval);
                    this.trainingInterval = null;
                }

                console.log('⏹️ Training stopped');
            }

            togglePause() {
                if (this.network.isTraining) {
                    this.stopTraining();
                    console.log('⏸️ Training paused');
                } else {
                    this.startTraining();
                    console.log('▶️ Training resumed');
                }
            }

            demonstrateForwardPass() {
                this.classifyCurrentImage();
                console.log('⚡ Forward pass demonstrated');
            }

            resetNetwork() {
                this.stopTraining();
                this.network = new SimpleCNN();

                document.getElementById('cnnEpochCount').textContent = '0';
                document.getElementById('cnnLossValue').textContent = '2.303';
                document.getElementById('cnnAccuracyValue').textContent = '10%';

                this.loadSampleImage('digit0');
                console.log('🔄 Network reset');
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            try {
                window.cnnApp = new CNNApp();
                console.log('🎉 CNN Visualizer loaded successfully!');
                console.log('Try drawing on the canvas or selecting different digits!');
            } catch (error) {
                console.error('❌ Error loading CNN Visualizer:', error);
            }
        });
    </script>
</body>
</html>
